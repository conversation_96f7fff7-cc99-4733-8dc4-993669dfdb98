import { FilterRangeTypes } from '../types/Filter.types'
import { RangeNumbers } from '../types/UtilFunctions.types'

const filterRangeTypes: FilterRangeTypes = [
  {
    type: 'today',
    label: 'FILTER_DATETYPE_TODAY',
  },
  {
    type: 'yesterday',
    label: 'FILTER_DATETYPE_YESTERDAY',
  },
  {
    type: 'last7Days',
    label: 'FILTER_DATETYPE_LAST_7_DAYS',
  },
  {
    type: 'last30Days',
    label: 'FILTER_DATETYPE_LAST_30_DAYS',
  },
  {
    type: 'customRange',
    label: 'FILTER_DATETYPE_CUSTOM_RANGE',
  },
]

const FilterTypeToNumber: Record<RangeNumbers, number> = {
  today: 0,
  yesterday: 1,
  last7Days: 7,
  last30Days: 30,
}

const START_DAY = 1
const JANUARY = 0
const DECEMBER = 11
const FEBRUARY = 1
const DEFAULT_YEAR_MIN = 1900
const DEFAULT_YEAR_MAX = 2100

const INTERCOM_APP_ID = 'n8tc7rdw'

export {
  DECEMBER,
  DEFAULT_YEAR_MAX,
  DEFAULT_YEAR_MIN,
  FEBRUARY,
  filterRangeTypes,
  FilterTypeToNumber,
  INTERCOM_APP_ID,
  JANUARY,
  START_DAY,
}

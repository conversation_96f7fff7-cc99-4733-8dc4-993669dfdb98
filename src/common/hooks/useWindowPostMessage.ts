import { useEffect } from 'react'
import { isLite } from '../../config/lite'
import { getWebsiteOrigin } from '../../features/authentication/utils/AuthFunctions'
import { allowedOrigins } from '../constants/TontineOrigins'

type TontineWebsiteOrigin = (typeof allowedOrigins)[number]

type EventData = {
  track: boolean
  consentCookie?: string
  // TODO: Type this
  changeConsent?: { marketing: boolean }
  cookieBot: object
}

type Event = MessageEvent<{
  source: 'MTL'
  // Only event Id issues from the webapp
  payload: {
    eventId: 'TERMINATE_REF_SESSION'
    eventData: EventData
  }
}>

const allowedEventsToListen = [
  'TERMINATE_REF_SESSION',
  'CONSENT_COOKIE',
  'CONSENT_CHANGE',
  'ARTICLE_CLICKED',
] as const
const allowedEventSources = ['MTL'] as const

/**
 * Listens for messages from allowed origins and allowed event ids
 */
const useMessageListener = (
  onMessageReceived: ({
    eventId,
    source,
    origin,
    eventData,
  }: {
    eventId:
      | 'TERMINATE_REF_SESSION'
      | 'CONSENT_CHANGE'
      | 'CONSENT_COOKIE'
      | 'ARTICLE_CLICKED'
    source: Event['source']
    origin: TontineWebsiteOrigin
    eventData: EventData
  }) => void
) => {
  useEffect(() => {
    const handleMessage = (event: Event) => {
      if (allowedOrigins.includes(event?.origin as TontineWebsiteOrigin)) {
        if (allowedEventsToListen.includes(event?.data?.payload?.eventId)) {
          // If there is no website origin in context
          // update the context
          onMessageReceived({
            eventId: event?.data?.payload?.eventId,
            source: event?.source,
            origin: event?.origin as TontineWebsiteOrigin,
            eventData: event?.data?.payload?.eventData,
          })
        }
      }
    }
    window.addEventListener('message', handleMessage)

    return () => {
      window.removeEventListener('message', handleMessage)
    }
  }, [])
}

/**
 * Sends the host parent a message. Fallback origin is `document.referrer` which
 * does not work directly with Safari private browsing
 */
const postMessageToParent = ({
  eventId,
  eventData,
  origin,
}: {
  eventId:
    | 'SUCCESS_VERIFY'
    | 'REDIRECT'
    | 'TERMINATE_WEB_SESSION'
    | 'ASK_FOR_CONSENT_COOKIE'
  eventData?: object | string
  origin?: string | null
}) => {
  const recipientOrigin = origin || getWebsiteOrigin()
  // Avoids spamming full version of the app with post event messages
  if (isLite && recipientOrigin) {
    window?.parent?.postMessage(
      {
        source: allowedEventSources[0],
        payload: { eventId, eventData },
      },
      recipientOrigin
    )
  }
}

export { useMessageListener, postMessageToParent }

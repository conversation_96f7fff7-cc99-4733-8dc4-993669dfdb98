@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

@mixin message-container(
  $bg-color: colors.$blue-faint,
  $border: none,
  $font-size: variables.$font-size-ml,
  $cursor: default
) {
  @include mixins.flex-layout;
  @include mixins.font-style($font-size: $font-size, $line-height: 1.625rem);
  background-color: $bg-color;
  gap: 30px;
  width: 100%;
  text-align: left;
  padding: 0.625rem;
  border: $border;
  border-radius: variables.$rounded;
  cursor: $cursor;
  @media only screen and (max-width: variables.$mobile-devices) {
    @include mixins.font-style(
      $font-size: variables.$font-size-s,
      $line-height: 20px
    );
  }
}

/** @define banner-message */
.banner-message {
  @include mixins.flex-layout;
  @include mixins.no-user-select;
  border-radius: variables.$rounded;

  &__container {
    @include message-container;

    &--info {
      @include message-container(
        $bg-color: colors.$white,
        $border: 1px solid colors.$blue,
        $cursor: default
      );
    }
  }

  &__icon {
    width: 28px;
    height: 28px;
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    background-color: colors.$white;
    &__container {
      gap: 1.5625rem;
      padding: 10px 30px;
    }
  }
}

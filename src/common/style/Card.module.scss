@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

// Mixin for card hover and active states
@mixin card-interact(
  $cursor: default,
  $bg-color: colors.$blue,
  $text-color: colors.$white,
  $image-filter: variables.$full-white-normal
) {
  background-color: $bg-color;
  cursor: $cursor;

  .card__image,
  .card__arrow {
    filter: $image-filter;
  }

  .card__title,
  .card__subtitle {
    color: $text-color;
  }
}

// Mixin for card content style changes
@mixin card-content-styles(
  $title-weight: variables.$font-semibold,
  $subtitle-weight: variables.$font-normal,
  $title-size: variables.$font-size-s,
  $subtitle-size: variables.$font-size-s,
  $article-gap: 0.5rem
) {
  .card__title {
    @include mixins.font-style(
      $font-weight: $title-weight,
      $font-size: $title-size,
      $line-height: small
    );
  }

  .card__subtitle {
    @include mixins.font-style(
      $font-weight: $subtitle-weight,
      $font-size: $subtitle-size,
      $line-height: small
    );
  }

  .card__body {
    gap: $article-gap;
  }
}

/** @define card */
.card {
  background-color: colors.$white;
  min-width: 20rem;
  padding: 1.25rem;
  transition: 0.25s ease-out;
  @include mixins.flex-layout(row, space-between, center, 0.75rem);
  @include mixins.no-user-select;

  &__header {
    @include mixins.flex-layout(column, center, center, 0.5rem);

    &--news {
      width: 100%;
      @include mixins.flex-layout();
    }

    &--box,
    &--box-alt {
      width: 100%;
      @include mixins.flex-layout(row, flex-start, center, 0.5rem);
    }
  }

  &__info {
    margin-left: auto;
    @include mixins.flex-layout($gap: 0.5rem);
  }

  &__body {
    margin-right: auto;
    @include mixins.flex-layout(column, flex-start, center);

    &--news {
      width: 100%;
    }

    &--box,
    &--box-alt {
      @include mixins.flex-layout(column, flex-start, flex-start);
    }

    &--feedback {
      transform: rotate(180deg) !important;
    }
  }

  &__footer {
    margin-left: auto;
    @include mixins.flex-layout($gap: 0.5rem);

    &--news,
    &--box,
    &--box-alt {
      width: 100%;
    }

    &--feedback {
      margin-left: unset;
    }
  }

  &__image {
    width: variables.$card-icon-size;
    height: variables.$card-icon-size;

    &--small {
      width: variables.$card-small-icon-size;
      height: variables.$card-small-icon-size;
    }

    &--large {
      width: variables.$card-large-icon-size;
      height: variables.$card-large-icon-size;
    }

    &--x-large {
      width: variables.$card-x-large-icon-size;
      height: variables.$card-x-large-icon-size;
    }

    &--news {
      height: unset;
      object-fit: cover;
      width: 100%;
      max-height: 180px;
      border-radius: variables.$rounded;
    }

    &--feedback {
      width: variables.$card-small-icon-size;
      height: variables.$card-small-icon-size;
    }

    @media only screen and (max-width: variables.$mobile-devices) {
      width: variables.$card-small-icon-size;
      height: variables.$card-small-icon-size;
      &--news {
        height: unset;
        object-fit: cover;
        width: 100%;
        max-height: 180px;
        border-radius: variables.$rounded;
      }
    }
  }

  &__title {
    width: 100%;
    @include mixins.font-style(
      $font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-ml,
      $line-height: small
    );

    &--feedback {
      @include mixins.font-style($font-size: variables.$font-size-s);
    }

    @media only screen and (max-width: variables.$mobile-devices) {
      @include mixins.font-style(
        $font-weight: variables.$font-semibold,
        $font-size: variables.$font-size-s,
        $line-height: small
      );
    }
  }

  &__subtitle {
    width: 100%;
    margin-top: 0.3125rem;
    @include mixins.font-style(
      $font-size: variables.$font-size-s,
      $line-height: small
    );

    @media only screen and (max-width: variables.$mobile-devices) {
      @include mixins.font-style(
        $font-size: variables.$font-size-xs,
        $line-height: small
      );
    }
  }

  &__arrow {
    height: variables.$button-icon-size;
    width: variables.$button-icon-size;

    &--feedback {
      transform: rotate(-180deg);
      width: variables.$card-small-icon-size;
      height: variables.$card-small-icon-size;
    }

    &--up {
      transform: rotate(-90deg);
    }

    &--down {
      transform: rotate(90deg);
    }

    &--hidden {
      visibility: hidden;
    }
  }

  &__alert {
    background-color: colors.$alert-amber;
    border-radius: variables.$rounded-full;
    width: variables.$card-icon-size;
    height: variables.$card-icon-size;
    @include mixins.flex-layout;
    @include mixins.font-style(
      $color: white,
      $font-size: variables.$font-size-xs,
      $line-height: none,
      $font-weight: variables.$font-bold
    );

    &--completed,
    &--error,
    &--warn,
    &--pending {
      background-color: colors.$white;
    }

    @media only screen and (max-width: variables.$mobile-devices) {
      width: variables.$card-small-icon-size;
      height: variables.$card-small-icon-size;
    }
  }

  &--off {
    border-radius: 0;
  }

  &--rounded-sm {
    border-radius: variables.$rounded-sm;
  }

  &--rounded {
    border-radius: variables.$rounded;
  }

  &--rounded-l {
    border-radius: variables.$rounded-l;
  }

  &--box {
    padding: 1rem;
    min-width: unset;
    @include mixins.flex-layout(column, flex-start, flex-start, 0.5rem);
    @include card-content-styles;
  }

  &--box-alt {
    padding: 1rem;
    min-width: unset;
    @include mixins.flex-layout(column, flex-start, flex-start, 0.5rem);
    @include card-content-styles(
      $subtitle-size: variables.$font-size-l,
      $subtitle-weight: variables.$font-bold
    );
  }

  &--stat {
    background-color: colors.$blue-faint;
    @include card-content-styles(
      $title-size: variables.$font-size-m,
      $subtitle-size: variables.$font-size-l,
      $subtitle-weight: variables.$font-semibold
    );
  }

  @media only screen and (min-width: variables.$mobile-devices) {
    &--enable-interact {
      &:hover {
        @include card-interact(pointer, colors.$blue-light, colors.$white);
      }

      &:active {
        @include card-interact(pointer, $text-color: colors.$white);
      }
    }
  }

  &--gray-dirty {
    background-color: colors.$gray-faint;

    @media only screen and (min-width: variables.$mobile-devices) {
      &--enable-interact {
        &:hover {
          opacity: 0.7;
          @include card-interact(
            pointer,
            colors.$gray-faint,
            colors.$gray-dark,
            none
          );
        }

        &:active {
          opacity: 1;
          @include card-interact(
            pointer,
            colors.$blue-faint,
            colors.$gray-dark,
            none
          );
        }
      }
    }
  }

  &--news {
    max-width: 25rem;
    min-width: unset;
    @include mixins.flex-layout(column, center, center, 0.5rem);

    &--enable-interact {
      &:hover,
      &:active {
        @include card-interact(pointer, colors.$white, colors.$blue, none);
      }
    }
  }

  &--feedback {
    writing-mode: vertical-lr;
    text-orientation: mixed;
    min-width: unset;
    width: fit-content;
    height: fit-content;
    position: fixed;
    left: 0;
    right: 0;
    margin-left: auto;
    top: 30%;
    @include mixins.flex-layout(row-reverse, space-around, $gap: 1.25rem);
    @include card-interact(pointer, colors.$green, colors.$white);

    //Mobile devices scss starts from here
    @media only screen and (max-width: variables.$mobile-devices) {
      display: none;
    }

    &--enable-interact {
      &:hover,
      &:active {
        opacity: 0.7;
        @include card-interact(pointer, colors.$green, colors.$white);
      }

      &:active {
        scale: 1.05;
      }
    }
  }

  &--pointer {
    cursor: pointer;
  }

  &--active {
    @include card-interact($text-color: colors.$white);

    &:hover,
    &:active {
      @include card-interact($text-color: colors.$white);
    }
  }

  &--disabled {
    box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.25);
    @include mixins.disabled;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    min-width: 100%;
  }

  @media only screen and (max-width: variables.$mobile-devices-s) {
    min-width: 100%;
  }
}

/** @define navigation-card */
.navigation-card {
  width: 100%;
}

/** @define extendedCard */
.extendedCard {
  overflow: hidden;
  max-width: 100%;

  &--payout {
    @include mixins.flex-layout($align-items: unset, $gap: 0.75rem);

    @media only screen and (max-width: variables.$mobile-devices) {
      @include mixins.flex-layout(column, $align-items: unset);
    }
  }

  &--off {
    border-radius: 0;
  }

  &--rounded-sm {
    border-radius: variables.$rounded-sm;
  }

  &--rounded {
    border-radius: variables.$rounded;
  }

  &--rounded-l {
    border-radius: variables.$rounded-l;
  }
}

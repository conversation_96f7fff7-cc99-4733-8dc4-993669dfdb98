@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define modal */
.modal {
  position: fixed;
  z-index: variables.$modal-z-index;
  left: 0;
  top: 5;
  width: 100%;
  height: 100%;
  filter: blur(5px);

  &--backdrop {
    @include mixins.modal-backdrop($z-index: variables.$modal-backdrop-z-index);
  }

  &--backdrop-full-color {
    @include mixins.modal-backdrop(
      $z-index: variables.$modal-backdrop-z-index,
      $background: colors.$blue-faint
    );
  }

  &__content {
    display: flex;
    justify-content: center;
    // Top nav height is 100px so this makes sure the modal never goes behind
    // the nav bar
    margin-top: calc(3% + 100px);
    @include mixins.no-user-select;
  }

  &__inner {
    position: relative;
  }

  &__close-icon {
    position: absolute;
    cursor: pointer;
    right: 10px;
    top: 5px;
    width: 30px;
    height: 30px;
    z-index: 999999;
    background-color: colors.$white;
  }

  &__content-wrapper {
    max-height: 70vh;
    overflow-y: scroll;
    position: relative;
    @include mixins.flex-layout(column, null, null);
    background-color: colors.$white;
    max-width: 21.5rem;
    border-radius: variables.$rounded;
    padding: 30px;
    box-shadow: 0px 4px 16px colors.$black-light;
    &--wide {
      max-width: 43.625rem;
    }
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__content {
      // Double the height of the mobile nav
      margin-top: 6.25rem;
      &--middle-content {
        display: flex;
        justify-content: center;
        margin-top: 60%;
      }
    }
  }
}

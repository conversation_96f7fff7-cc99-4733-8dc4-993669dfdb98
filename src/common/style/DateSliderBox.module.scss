@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

/** @define date-slider-box */
.date-slider-box {
  margin-bottom: 1.125rem;
  // scale: 0.95;

  &__label {
    position: static;
  }

  &__bg {
    background-color: colors.$white;
    border-radius: variables.$rounded;
  }

  &__inner {
    @include mixins.flex-layout;
    background-color: colors.$white;
    border: colors.$white-faint 1px solid;
    border-radius: variables.$rounded;

    &:hover {
      border: colors.$blue-light 1px solid;
    }
  }

  &__container {
    @include mixins.flex-layout($justify-content: space-between);
  }

  &__range {
    margin-top: 3.75rem;
  }

  &__caption {
    text-align: left;
  }
}
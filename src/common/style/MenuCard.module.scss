@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

@mixin menu-card-title($fz: variables.$font-size-l) {
  overflow: clip;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0;
  min-height: 33px;
  font-size: $fz;

  @media only screen and (max-width: variables.$mobile-devices) {
    white-space: unset;
    text-align: center;
  }
}

@mixin menu-card {
  font-weight: variables.$font-semibold;
  color: colors.$gray-dark;
  cursor: pointer;
  max-width: 21.875rem;
  margin-bottom: 1.25rem;
  @include mixins.flex-layout(column, none, flex-start);

  @media only screen and (max-width: variables.$mobile-devices) {
    width: 100%;
    max-width: unset;
  }
}

/** @define menu-card */
.menu-card {
  @include menu-card;

  &--coming-soon {
    @include mixins.disabled;
    @include menu-card;
  }

  &__title {
    @include menu-card-title;

    &--alternative {
      @include menu-card-title($fz: variables.$font-size-m);
    }
  }

  @media screen and (max-width: 320px) {
    &__title {
      font-size: variables.$font-size-m;
      padding: 0 20px;
    }
  }
}

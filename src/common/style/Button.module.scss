@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 colors.$green;
  }
  50% {
    box-shadow: 0 0 0 10px #4caf5000;
  }
  100% {
    box-shadow: 0 0 0 0 #4caf5000;
  }
}

@mixin animate-button($bg-color: colors.$green) {
  &--animated {
    animation: pulse 3.5s infinite;
    @include mixins.button;
    &:hover {
      animation: none;
    }
  }
}

/** @define button */
.button {
  &__icon {
    height: variables.$button-icon-size;
    width: variables.$button-icon-size;
  }

  &__loading-animation {
    width: 100px;
    height: 100px;
    flex-basis: 40%;
  }

  &--primary {
    @include mixins.button;
    @include animate-button;
  }

  &--blue {
    @include mixins.button($bg-color: colors.$blue);
  }

  &--blue-dark {
    @include mixins.button($bg-color: colors.$blue-dark);
  }

  &--gray-dark {
    @include mixins.button($bg-color: colors.$gray-dark);
  }

  &--back {
    flex-basis: 30%;
    @include mixins.button(
      $padding: 13px 15px 14px 5px,
      $bg-color: colors.$white,
      $font-color: colors.$blue,
      $icon-gap: 5px,
      $justify-button-inner: center,
      //4 characters
      $char-limit: 6ch
    );
  }

  &--back--light {
    flex-basis: 30%;
    @include mixins.button(
      $padding: 13px 15px 14px 5px,
      $bg-color: colors.$gray-faint,
      $font-color: colors.$blue,
      $icon-gap: 5px,
      $justify-button-inner: center
    );
  }

  &--loading {
    pointer-events: none;
    @include mixins.button(colors.$blue-light, $cursor: progress);
  }

  &--logout {
    padding: 0.3125rem;
    @include mixins.button(
      colors.$blue-darker,
      $height: 40px,
      $width: 120px,
      $icon-gap: 5px,
      $font-size: variables.$font-size-s
    );
  }

  &--secondary {
    @include mixins.button(
      colors.$white,
      colors.$red,
      2px solid colors.$red,
      $height: auto
    );
  }

  &--danger {
    width: 100%;
    @include mixins.button($bg-color: colors.$red);
  }

  &--alternative {
    @include mixins.button(colors.$white, colors.$blue, 2px solid colors.$blue);
  }

  &--login {
    @include mixins.button(
      colors.$white,
      colors.$blue,
      2px solid colors.$blue,
      $width: 114px
    );
    //Login button is different for mobile devices
    @media only screen and (max-width: variables.$mobile-devices) {
      @include mixins.button(
        colors.$white,
        colors.$blue,
        1px colors.$blue solid,
        30px,
        70px,
        $padding: 0
      );
    }
  }

  &--signup-desktop {
    @include mixins.button(
      transparent,
      colors.$white,
      colors.$white 1px solid,
      $width: 114px
    );
  }

  &--select-value {
    @include mixins.button(
      colors.$white,
      colors.$gray-dark,
      variables.$white-faint-border
    );
  }

  &--text-only {
    text-decoration: underline;
    @include mixins.button(transparent, colors.$blue);
  }

  &--yellow {
    @include mixins.button(
      $bg-color: colors.$yellow,
      $font-color: colors.$gray-dark
    );
  }
}

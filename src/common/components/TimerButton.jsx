import PropTypes from 'prop-types'
import <PERSON><PERSON> from './Button'
import CountDownTimer from './CountDownTimer'

/**
 * Button that displays a countdown timer next to the button, only
 * one parameter of `hours`, `minutes` or `seconds needs to be passed to start
 * the countdown
 */
const TimerButton = ({
  hours,
  minutes,
  seconds,
  disabled,
  children,
  onClick,
  onCountdownFinished,
  trackActivity,
  variant,
  loading,
}) => {
  return (
    <Button
      loading={loading}
      disabled={disabled}
      onClick={onClick}
      variant={variant}
      trackActivity={trackActivity}
    >
      {!disabled ? (
        children
      ) : (
        <>
          {`${children} `}
          (
          <CountDownTimer
            hours={hours}
            minutes={minutes}
            seconds={seconds}
            onCountdownFinished={onCountdownFinished}
          />
          )
        </>
      )}
    </Button>
  )
}

TimerButton.propTypes = {
  onClick: PropTypes.func.isRequired,
  onCountdownFinished: PropTypes.func,
  hours: PropTypes.number,
  minutes: PropTypes.number,
  seconds: PropTypes.number,
  disabled: PropTypes.bool,
  children: PropTypes.node,
  variant: PropTypes.string,
  trackActivity: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
  loading: PropTypes.bool,
}

export default TimerButton

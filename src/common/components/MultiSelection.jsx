import PropTypes from 'prop-types'
import React from 'react'
import { useState } from 'react'
import style from '../style/MultiSelection.module.scss'

//FIXME: REACT ANTI PATTERN THIS SHOULD BE A SEPARATE COMPONENT!!!
/**
 * @param {string} title The title of the selection.
 * @param {string} desc The short description that will be displayed below the
 * tile of the selection
 * @param {boolean} activeClassName The class name that determines if the
 * selection is selected or not
 * @param {function} onSelectionClick Returns the selected item
 * @param {function} setMultiSelectionData  Updates the multiSelectionData
 * @param {object} multiSelectionData The object that contains the active selection
 *
 * @description Select item that contains the data of the selection
 */

const Selection = ({ title, desc, activeClassName, onSelectionClick }) => {
  //Allow multiple items to be active concurrently
  const [active, setActive] = useState(false)
  const multipleActiveItems = () => (active ? 'active' : 'inactive')

  return (
    <div
      className={style[`multi-selection__list-item`]}
      onClick={(data) => {
        setActive((prev) => !prev)
        onSelectionClick(data)
      }}
    >
      <div>
        <p className={style['multi-selection__title']}>{title}</p>
        {desc && <p className={style['multi-selection__sub']}>{desc}</p>}
      </div>
      <div
        className={
          style[
            `multi-selection__action--${
              //If activeClassName is passed in then the component is in unique
              //mode, meaning only one item can be selected, otherwise multiple
              //items can be selected
              activeClassName ? activeClassName : multipleActiveItems()
            }`
          ]
        }
      />
    </div>
  )
}

Selection.propTypes = {
  title: PropTypes.string,
  desc: PropTypes.string,
  activeClassName: PropTypes.string,
  onSelectionClick: PropTypes.func,
}

/**
 * @param {string} multiSelectionCardLabel - Title of the multi-selections card.
 * @param {array} values - Array of all the selections which needs to be
 * displayed.
 * @param {object} activeSelection - Default value selected value
 * @param {function} onSelection - Returns the object of the selected selection
 * from this component.
 * @param {boolean} allowMultipleItems Allows selecting multiple items
 *
 * @description  Renders a multi selection component with cards as items,
 * contains a checkmark icon to indicate if the selection is selected or not.
 */

const MultiSelection = ({
  multiSelectionCardLabel,
  onSelection,
  setMultiSelectionData,
  multiSelectionData,
  allowMultipleItems,
}) => {
  // This function is called when the users clicks on a selection, then the object of that selection is selected and gets returned in the onSelection prop function of this component.
  const selectItem = (item) => {
    setMultiSelectionData((prevState) => {
      return {
        ...prevState,
        activeSelection: item,
      }
    })
    //getActiveStyles(item)
    onSelection(item)
  }

  // This function activates the selected selection and deactivate other selections
  const getActiveStyles = (selection) => {
    return selection?.id === multiSelectionData?.activeSelection?.id
      ? 'active'
      : 'inactive'
  }

  /**
   * @param {string} selection - Selection item that needs to be checked if it has
   * a `title` property
   *
   * @description Checks if the passed in argument has a title, so it can render
   * it as label for selection item. Otherwise we assume it is the currencies
   * from the currency API so a title symbol is added next to the label
   *
   * @note TODO: This is a quick patch. Change this soon as possible when the
   * design is a bit concrete!
   */
  const checkIfSelectionHasTitle = (selection) =>
    selection?.title ? selection?.title : selection

  // This function renders all the values which this component gets through props in the form of an array.
  const renderMultiSelections = multiSelectionData?.data?.map(
    (selection, index) => {
      return (
        <React.Fragment key={`${selection?.title}${index}`}>
          <Selection
            title={checkIfSelectionHasTitle(selection)}
            desc={selection?.desc}
            activeClassName={
              allowMultipleItems ? null : getActiveStyles(selection)
            }
            onSelectionClick={() => selectItem(selection)}
          />
        </React.Fragment>
      )
    }
  )

  return (
    <div className={style['multi-selection']}>
      <p className={style['multi-selection__label']}>
        {multiSelectionCardLabel}
      </p>
      <div className={style['multi-selection__container']}>
        {renderMultiSelections}
      </div>
    </div>
  )
}

MultiSelection.propTypes = {
  multiSelectionCardLabel: PropTypes.string,
  values: PropTypes.array,
  activeSelection: PropTypes.object,
  onSelection: PropTypes.func,
  setMultiSelectionData: PropTypes.func.isRequired,
  multiSelectionData: PropTypes.object.isRequired,
  allowMultipleItems: PropTypes.bool,
}

export default MultiSelection

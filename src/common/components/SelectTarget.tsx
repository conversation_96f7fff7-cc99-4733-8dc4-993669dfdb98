import { useTranslate } from '../hooks/useTranslate'
import { SelectTargetProps } from '../types/Select.types'
import SelectValue from './SelectValue'

/**
 * Renders buttons with `deposit` and `target` values
 */
const SelectTarget = ({
  target,
  setTarget,
  trackActivities,
}: SelectTargetProps) => {
  const t = useTranslate()
  return (
    <SelectValue
      label={t('TARGET_INCOME_LABEL')}
      optional
      value={target}
      setValue={setTarget}
      optionsToSelect={['deposit', 'target']}
      buttonLabels={[t('INITIAL_DEPOSIT_VALUE'), t('MONTHLY_TARGET_LABEL')]}
      trackActivities={trackActivities}
    />
  )
}

export default SelectTarget

import style from '../style/MenuCard.module.scss'
import { MenuCardProps } from '../types/MenuCard.types'

/**
 * Menu card for menu items, serves as a container for the menu card item
 */
const MenuCard = ({ title, children, variant, comingSoon }: MenuCardProps) => {
  return (
    <div className={style[`menu-card${comingSoon ? '--coming-soon' : ''}`]}>
      <div
        className={style[`menu-card__title${variant ? `--${variant}` : ''}`]}
      >
        {title}
      </div>
      {children}
    </div>
  )
}

export default MenuCard

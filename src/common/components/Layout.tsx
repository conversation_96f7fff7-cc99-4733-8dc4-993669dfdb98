import style from '../style/Layout.module.scss'
import { LayoutProps } from '../types/Layout.types'
import Divider from './Divider'
import DividerHeader from './DividerHeader'
import MobileHeader from './MobileHeader'
import PageLayout from './PageLayout'

/**
 * Layout wrapper for page's content, mobile and desktop. Some of
 * common components that appear above the main page content can be rendered
 * here by just passing the needed arguments, or you can pass in any component
 * to sit above or below the page's content
 */
const Layout = ({
  children,
  pageTitle,
  navigateTo,
  onClickAction,
  headerTitle,
  hideMobileHeader,
  className,
  hideDividerHeader,
  card,
  bottomSection,
  dividersClass,
  topSection,
  hideBottomSectionDivider,
  textNextToHeader,
  headerTextColor,
  containerWidth,
  headerVariant,
  layoutVariant,
  containerHeight,
  containerMt,
}: LayoutProps) => {
  return (
    <main className={className}>
      <div className={style[`layout__top-section-mobile`]}>
        {topSection && topSection}
      </div>

      {!hideDividerHeader && (
        <DividerHeader
          headerText={pageTitle}
          className={dividersClass}
          additionalText={textNextToHeader}
        />
      )}

      {!hideMobileHeader && (
        <MobileHeader
          pageTitle={pageTitle}
          to={navigateTo}
          onClick={onClickAction}
          className={className}
        />
      )}

      {/* Mobile specific case where we need a top element to be below the mobile header */}
      <div className={style[`layout__top-section-desktop`]}>
        {topSection && topSection}
      </div>

      {card && (
        <>
          {card}
          <Divider className={`${dividersClass}`} />
        </>
      )}

      <PageLayout
        containerWidth={containerWidth}
        headerTitle={headerTitle}
        headerTextColor={headerTextColor}
        headerVariant={headerVariant}
        layoutVariant={layoutVariant}
        containerHeight={containerHeight}
        containerMt={containerMt}
      >
        {children}
      </PageLayout>

      {bottomSection && (
        <>
          {!hideBottomSectionDivider && (
            //TODO: THIS GOTTA BE HIDDEN ON MOBILE EVERY PAGE
            <Divider
              className={`${style[`layout__div-bottom`]} ${dividersClass}`}
            />
          )}
          {bottomSection}
        </>
      )}
    </main>
  )
}

export default Layout

import { ASSET } from '../constants/Assets'
import style from '../style/BannerMessage.module.scss'
import { BannerMessageProps } from '../types/Banner.types'
import Icon from './Icon'

/**
 * Renders a message with specified props
 */
const BannerMessage = ({
  children,
  className,
  hideIcon,
  variant,
}: BannerMessageProps) => {
  return (
    <article className={`${style['banner-message']} ${className ?? ''}`}>
      <div
        className={
          style[`banner-message__container${variant ? `--${variant}` : ''}`]
        }
      >
        {!hideIcon && (
          <Icon
            className={style[`banner-message__icon`]}
            fileName={ASSET.infoCircle}
          />
        )}
        <section className={style[`banner-message__text`]}>{children}</section>
      </div>
    </article>
  )
}

export default BannerMessage

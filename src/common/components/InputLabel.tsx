import { ASSET } from '../constants/Assets'
import { CONSTANTS } from '../constants/ConstantValues'
import style from '../style/InputLabel.module.scss'
import { InputLabelProps } from '../types/InputLabel.types'
import Icon from './Icon'
import TontineTooltip from './Tooltip'
/**
 *  Renders a text label used for input fields. If `infoIcon` prop is passed in
 *  an info icon will be rendered next to the label
 */
const InputLabel = ({
  label = '',
  className = '',
  infoIcon,
  tooltipText,
  renderAsterisk,
}: InputLabelProps) => {
  if (infoIcon) {
    return (
      <div className={style.inputLabel}>
        {/* biome-ignore lint/a11y/noLabelWithoutControl: <See if can change to be controlled> */}
        <label
          className={`${style['inputLabel__input-label']} ${style['inputLabel__tooltip-label']} ${className} `}
        >{`${label}${renderAsterisk ? CONSTANTS.REQUIRED_FIELD : ''}`}</label>

        <TontineTooltip content={tooltipText}>
          <Icon
            fileName={ASSET.infoIconSmall}
            className={style[`inputLabel__icon`]}
          />
        </TontineTooltip>
      </div>
    )
  }

  return (
    // biome-ignore lint/a11y/noLabelWithoutControl: <See if can change to be controlled>
    <label className={`${style['inputLabel__input-label']} ${className}`}>
      {label ? `${label}${renderAsterisk ? CONSTANTS.REQUIRED_FIELD : ''}` : ''}
    </label>
  )
}

export default InputLabel

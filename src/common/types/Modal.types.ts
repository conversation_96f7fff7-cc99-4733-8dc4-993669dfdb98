import { TrackActivity } from './CommonTypes.types'

type Backdrop = 'full-color'

type CSSModuleClasses = { readonly [key: string]: string }

type ModalProps = {
  children: React.ReactNode
  isOpen: boolean
  backdrop: boolean
  hasOnlyContent?: boolean
  onOutsideModalContentClick?: () => void
  showCloseButton?: boolean
  onClickCloseButton?: () => void
  wide?: boolean
  /**
   * Renders full color backdrop, default is with reduced opacity
   */
  backdropType?: Backdrop
  variant?: 'middle-content'
  dataTestID?: string
  className?: string
  customStyle?: CSSModuleClasses
  trackActivityBackDrop?: TrackActivity
  trackActivityCloseButton?: TrackActivity
}

export type { Backdrop, ModalProps }

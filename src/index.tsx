import { createRoot } from 'react-dom/client'
import App from './App'

import { BrowserOptions, init } from '@sentry/react'
import { envColor, environment, sentry } from 'MyTontineConfig'
import { ENVIRONMENTS } from './common/constants/ConstantValues'
import {
  consoleWarningMessage,
  getIpGeoLocation,
} from './common/utils/UtilFunctions'

init(sentry as BrowserOptions)

// Won't work on localhost or local dev env
if (environment !== ENVIRONMENTS.development) {
  getIpGeoLocation()
}

if (environment !== ENVIRONMENTS.development) {
  consoleWarningMessage()
}

console.log(`Using config: %c${environment}`, `color:${envColor}`)

const container = document.querySelector('#root')
// biome-ignore lint/style/noNonNullAssertion: <TODO: Find solution maybe>
const root = createRoot(container!)

root.render(<App />)

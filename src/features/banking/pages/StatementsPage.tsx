import { useState } from 'react'
import Filters from '../../../common/components/Filters'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ACCOUNT_MENU, FUND_PENSION } from '../../../routes/Route'
import NoTransactions from '../components/NoTransactions'
import StatementsFilters from '../components/StatementsFilters'
import Transactions from '../components/Transactions'
import { useBankingService } from '../hooks/useBankingService'
import style from '../style/StatementsPage.module.scss'
import { combinedFilters, transactionStartAndEnd } from '../utils/UtilFunctions'
import { statementTypes } from '../utils/consts'

/**
 * Renders user's statements payin and payout with filters
 */
const StatementsPage = () => {
  const t = useTranslate()
  const navigate = useCustomNavigation()
  const { bankContext } = useBankingService()

  const transactions = bankContext?.bankingInfo
  const allTransactions = transactions?.['all']
  const statementsTypeAll = {
    ...statementTypes[2],
    label: t(statementTypes[2].label),
  }

  const { start: firstTransactionDate, end: lastTransactionDate } =
    transactionStartAndEnd(transactions?.['all'] ?? [])

  const [statementType, setStatementType] = useState(statementsTypeAll)
  const [dateFilteredTransactions, setDateFilteredTransactions] =
    useState(allTransactions)
  const [filteredData, setFilteredData] = useState(allTransactions)

  return (
    <Layout
      containerMt="mt-20"
      navigateTo={ACCOUNT_MENU.FUND_PLAN_MENU}
      pageTitle={t('PAGE_TITLE_STATEMENTS')}
      bottomSection={
        <NavigationButtons
          onClickSecond={() => navigate(FUND_PENSION.INVESTMENT_ACCOUNT)}
          onClickFirst={() => navigate(ACCOUNT_MENU.FUND_PLAN_MENU)}
          secondButtonLabel={t('CONTRIBUTION_HISTORY.TO_FUND_PENSION')}
        />
      }
    >
      <main className={style['statementsPage__page-container']}>
        {allTransactions && allTransactions?.length > 0 && (
          <Filters
            t={t}
            array={transactions?.['all'] ?? []}
            defaultFromDate={firstTransactionDate}
            defaultToDate={lastTransactionDate}
            filterKey={{
              range: 'transaction.time',
            }}
            onFiltersApplied={(dateFilteredArray) => {
              setDateFilteredTransactions(dateFilteredArray)
              setFilteredData(
                combinedFilters({
                  array: dateFilteredArray,
                  filterBy: statementType?.value ?? 'All',
                  filterType: "type'",
                })
              )
            }}
            onResetFilters={() => {
              setStatementType(statementsTypeAll)
              setFilteredData(allTransactions)
            }}
          >
            <StatementsFilters
              statementTypeLabel={t('STATEMENTS_TYPE_FILTER_LABEL')}
              statementType={statementType}
              statementOptions={statementTypes.map((item) => ({
                ...item,
                label: t(item.label),
              }))}
              onStatementTypeChange={(option) => {
                setStatementType(option)
                setFilteredData(
                  combinedFilters({
                    array: dateFilteredTransactions ?? allTransactions,
                    filterBy: option?.value,
                    filterType: "type'",
                  })
                )
              }}
            />
          </Filters>
        )}

        {filteredData && filteredData?.length > 0 ? (
          <section className={style['statementsPage__statements-container']}>
            <Transactions
              transactions={filteredData ?? []}
              className={style['statementsPage__statements']}
            />
          </section>
        ) : (
          <NoTransactions>
            {t('BANKING.CONTRIBUTIONS_HISTORY_FILTER_FEEDBACK')}
          </NoTransactions>
        )}
      </main>
    </Layout>
  )
}

export default StatementsPage

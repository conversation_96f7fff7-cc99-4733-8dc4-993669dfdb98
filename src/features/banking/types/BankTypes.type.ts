type Amount = {
  amount: number
  currency: string
}

type HistoryTransaction = {
  transaction: {
    time: Date
    "type'": string
    amount: Amount
  }
  nominalBalance: Amount
  totalContribution: Amount
}

type ReferredCount = {
  amount: {
    amount: number
    currency: string
  }
  payoutMethod: {
    contents: Array<string>
    tag: 'BankAccount'
  }
  referralCode: string
  time: Date
}

type BankingInfo = Partial<{
  all: Array<HistoryTransaction>
  payoutHistory: Array<HistoryTransaction>
  payinHistory: Array<HistoryTransaction>
  nominalBalance: Array<unknown>
  nextPayout: unknown
}>

export type { BankingInfo, HistoryTransaction, ReferredCount }

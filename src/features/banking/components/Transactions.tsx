import ExtendedContentCard from '../../../common/components/ExtendedContentCard'
import { ASSET } from '../../../common/constants/Assets'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { convertDateToClientLocale } from '../../../common/utils/UtilFunctions'
import ExtendedContent from '../pages/ExtendedContent'
import { HistoryTransaction } from '../types/BankTypes.type'

/**
 * Renders cards for each transaction
 */
const Transactions = ({
  transactions,
  className,
}: {
  transactions: Array<HistoryTransaction>
  className?: string
}) => {
  const t = useTranslate()
  const { formatAmount } = useLocalization()

  return (
    transactions?.map((contribution) => {
      return (
        <ExtendedContentCard
          className={className}
          key={contribution?.transaction?.time?.toString()}
          headerImage={ASSET.iconaccountcontributionbicolorUS}
          title={
            formatAmount({
              amount: contribution?.transaction?.amount?.amount ?? 0,
              currency: contribution?.nominalBalance?.currency,
              style: 'currency',
            })?.formattedAmountWithSymbol ?? ''
          }
          variant="gray-dirty"
          expandClickDisabled
          autoExpand
          dataTestID={UI_TEST_ID?.contributionCard}
        >
          <ExtendedContent
            date={
              convertDateToClientLocale(
                contribution?.transaction?.time,
                CONSTANTS.CLOSURE_ACCOUNT_DATE_FORMAT
              )?.formattedLocaleDate
            }
            firstLineLabel={t('PAYOUT_EXTENDED.FIRST_LINE_LABEL')}
            variant="gray-dirty"
          />
        </ExtendedContentCard>
      )
    }) ?? []
  )
}

export default Transactions

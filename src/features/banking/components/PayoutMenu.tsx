import Layout from '../../../common/components/Layout'
import MenuCard from '../../../common/components/MenuCard'
import MenuCardItem from '../../../common/components/MenuCardItem'
import SecondaryMenuContainer from '../../../common/components/SecondaryMenuContainer'
import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PAYOUT, PRIVATE } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'

/**
 *  Payout menu for different payout sections in the app
 */
const PayoutMenu = () => {
  const {
    context: { user_details },
  } = useAccountService()
  const t = useTranslate()

  return (
    <Layout
      navigateTo={PRIVATE.ACCOUNT}
      pageTitle={t('ACCOUNT.MENU_ITEM_PAYOUT_SETUP')}
    >
      <SecondaryMenuContainer>
        <MenuCard
          title={t('BANKING.PAYOUT_ACC_COUNTAINER_TITLE')}
          variant="alternative"
        >
          <MenuCardItem
            mainText={t('BANKING.CARD_WHERE_TO_SEND_YOUR_INCOME_LABEL')}
            to={PAYOUT.SETUP}
            icon={ASSET.iconaccountaddbank}
            writeProtected={!user_details?.payout_account}
            dataTestID={UI_TEST_ID.incomeSubMenuPayoutDetails}
            cardVariant="gray-dirty"
          />
        </MenuCard>

        <MenuCard
          title={t('BANKING.CONTAINER_SET_PAYOUT_START_MONTH')}
          variant="alternative"
          comingSoon
        >
          <MenuCardItem
            disabled={true}
            mainText={t('PAYOUT_SETTINGS.PAGE_TITLE')}
            writeProtected
            icon={ASSET.iconaccountaddbank}
            cardVariant="gray-dirty"
          />
        </MenuCard>
      </SecondaryMenuContainer>
    </Layout>
  )
}

export default PayoutMenu

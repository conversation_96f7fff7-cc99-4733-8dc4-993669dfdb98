import { useState } from 'react'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import { useUserInfo } from '../../authentication/hooks/FormHooks'
import { useUserInfoValidation } from '../../authentication/hooks/useUserInfoValidation'
import { UseEditReferralCodeProps } from '../types/ReferralTypes.type'
import { useSaveReferralCode } from './useSaveReferralCode'

/**
 * Handles code editing and saving logic
 */
export const useEditReferralCode = ({
  referralLink,
  setOpenEditModal,
}: UseEditReferralCodeProps) => {
  const { referralCodeValidated, validateReferralCode } =
    // Necessary conversion to override the default type from JS
    useUserInfoValidation()

  const { referralCode, setReferralCode } = useUserInfo({
    referralCode: referralLink.replaceAll(CONSTANTS.REFERRAL_CODE_PREFIX, ''),
  })

  const [editCode, setEditCode] = useState(false)

  const { handleSaveReferralCode, apiError, setApiError, isLoading } =
    useSaveReferralCode({
      referralLink: referralCode ?? '',
      setOpenEditModal,
    })

  const onModalClose = () => {
    setEditCode(false)
    setOpenEditModal(false)
  }

  const handleOnChange = (value: string) => {
    setReferralCode(value)
    //Removes an API error like bad word while the user is typing
    setApiError(null)
  }

  /**
   *  Disables the save button if there is an API error or a
   * validation error
   */
  const shouldDisableSaveButton = () =>
    !referralCodeValidated?.valid || Boolean(apiError)

  return {
    customReferralCode: referralCode,
    editCode,
    isLoading,
    setApiError,
    shouldDisableSaveButton,
    onModalClose,
    handleSaveReferralCode,
    handleOnChange,
    validateReferralCode,
    referralCodeValidated: {
      ...referralCodeValidated,
      ...apiError,
    },
  }
}

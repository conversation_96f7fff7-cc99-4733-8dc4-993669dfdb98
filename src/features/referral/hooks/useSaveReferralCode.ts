import { useState } from 'react'
import { toast } from 'react-toastify'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ErrorStorage } from '../../CommonState.type'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { UseEditReferralCodeProps } from '../types/ReferralTypes.type'

/**
 * Saves a referral code using state machines
 */
export const useSaveReferralCode = ({
  referralLink,
  setOpenEditModal,
}: UseEditReferralCodeProps) => {
  const [apiError, setApiError] = useState<{
    message?: string
    i18nKey?: string
  } | null>(null)

  const {
    send,
    context: { user_details },
    currentState,
  } = useAccountService()
  const t = useTranslate()

  const handleSuccessfulCodeEditing = () => {
    //Closes the referral code editing modal on successful edit
    setOpenEditModal(false)

    toast.success(t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT'))
  }

  const handleFailedReferralCodeSaving = (error?: ErrorStorage) => {
    setApiError({
      message: error?.translatedError,
      // Invalid chars are prevented on the frontend, so the only error that can
      // occur is the vulgar word error
      i18nKey: error?.translatedError,
    })
  }

  // This function is called when the user clicks on the save button in the referral-link-modal
  const handleSaveReferralCode = () => {
    send({
      type: 'CREATE_REFERRAL_CODE',
      payload: {
        // No idea what is going on with this "influencer referral codes"
        // this works ok for now
        refCodePrefix: user_details?.is_influencer
          ? ''
          : CONSTANTS.REFERRAL_CODE_PREFIX,
        referralCode: referralLink,
        successCallback: handleSuccessfulCodeEditing,
        failureCallback: (error) => handleFailedReferralCodeSaving(error),
      },
    })
  }

  return {
    apiError,
    handleSaveReferralCode,
    setApiError,
    isLoading: currentState === 'CREATING_REFERRAL_CODE',
  }
}

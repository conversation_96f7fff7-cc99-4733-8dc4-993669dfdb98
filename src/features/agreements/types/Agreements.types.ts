import { Dispatch, SetStateAction } from 'react'
import { RenderPageStates } from './CommonLegalTypes.types'

type AgreementContentProps = {
  onUserReadAgreement?: (hasRead: boolean) => void
  onAgreed?: ({
    checkboxChecked,
    agreementData,
  }: {
    checkboxChecked: boolean
    agreementData: unknown
  }) => void
  backgroundColor?: 'white'
  agreementData?: unknown
  agreementSigned?: boolean
  agreementImage: string
  agreementContent: string
  agreementHeading?: string
  checkboxLabel?: string
  agreementSigningError?: string
  readOnly?: boolean
}

type DispatchFunction = Dispatch<SetStateAction<RenderPageStates>>

export type { AgreementContentProps, DispatchFunction }

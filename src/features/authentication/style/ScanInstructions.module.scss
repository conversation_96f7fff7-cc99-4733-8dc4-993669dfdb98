@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define scanInstructions */
.scanInstructions {
  @include mixins.no-user-select;

  &__title {
    @include mixins.font-style(
      $font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-large
    );
    text-align: center;
    max-width: 25rem;
  }

  &__main-container {
    @include mixins.flex-layout(column);
    background-color: colors.$white;
    border-radius: variables.$rounded;
    margin: 0 0 1.875rem 0;
  }

  &__icon {
    height: 50px;
  }

  &__explainer {
    @include mixins.font-style($font-size: variables.$font-size-ml);
    text-align: center;
    max-width: 31.25rem;
    height: auto;
  }

  &__requirements-container {
    @include mixins.flex-layout(column, flex-start, flex-start, 1.25rem);
    margin-top: 2.25rem;
  }

  &__title-block {
  @include mixins.flex-layout(column, flex-start, flex-start, 0.25rem);
  width: 100%;
  }

  &__title-container {
    @include mixins.flex-layout(row, center, center, 0.625rem);
       @include mixins.font-style(
      $font-weight: variables.$font-semibold,
      $font-size: variables.$font-size-ml,
    );
    align-items: center;
  }

  &__subtext {
    @include mixins.font-style(
      $font-size: variables.$font-size-s,
    );
    color: colors.$gray-dark;
    background-color: colors.$blue-faint;
    padding: 15px 20px;
    margin-top: 0.625rem;
    width: 100%;
    border-radius: variables.$rounded;
  }
}

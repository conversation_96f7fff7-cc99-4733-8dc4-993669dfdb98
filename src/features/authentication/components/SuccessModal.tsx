import Button from '../../../common/components/Button'
import TimerButton from '../../../common/components/TimerButton'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import { ANIMATION } from '../../../common/constants/Animations'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useAccountService } from '../hooks/useAccountService'
import { millisecondsToSeconds } from '../utils/UtilsFunctions'

/**
 * Renders a success modal if the email was successfully sent,
 * `emailSuccessfullySent` function does the check if the app is in the
 * successfully sent email state
 */
const SuccessModal = ({
  userEmail,
  handleMagicLogin,
  resetAuthMachineToNoAuthState,
}: {
  userEmail: string | undefined
  handleMagicLogin: () => void
  resetAuthMachineToNoAuthState: () => void
}) => {
  const t = useTranslate()
  const { currentState } = useAccountService()

  return (
    <ConfirmationModal
      isOpen
      title={'SUCCESS.HEADERTEXT.MODAL'}
      content={'MODAL.BODY.TEXT.VERIFICATION.SENT'}
      contentValues={{
        email: userEmail,
      }}
      animatedIcon={ANIMATION.checkmark}
    >
      <Button onClick={resetAuthMachineToNoAuthState}>
        {t('SUCCESS.MODAL.DISMISS.BUTTON')}
      </Button>

      <TimerButton
        seconds={millisecondsToSeconds(
          CONSTANTS.RESEND_EMAIL_TIMER_MILLISECONDS
        )}
        variant="alternative"
        onClick={handleMagicLogin}
        disabled={currentState === 'SENT_NEW_TAB_EMAIL'}
      >
        {t('ACCOUNT.EMAIL_SEND_AGAIN_BUTTON')}
      </TimerButton>
    </ConfirmationModal>
  )
}

export default SuccessModal

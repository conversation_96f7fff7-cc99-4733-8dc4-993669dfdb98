import { useState } from 'react'
import { toast } from 'react-toastify'
import But<PERSON> from '../../../common/components/Button'
import LottieAnimation from '../../../common/components/LottieAnimation'
import Modal from '../../../common/components/Modal'
import ToastMessage from '../../../common/components/ToastMessage'
import { ANIMATION } from '../../../common/constants/Animations'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ErrorStorage } from '../../CommonState.type'
import { useAccountService } from '../hooks/useAccountService'

import TextError from '../../../common/components/TextError'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import { useSubmitPin } from '../hooks/useSubmitPin'
import style from '../style/SessionExtensionModal.module.scss'
import { PinType } from '../types/Pin.types'
import PinConfirmation from './PinConfirmation'
import PinInput from './PinInput'
import SessionExtensionTimerAndTitle from './SessionExtensionTimerAndTitle'

/**
 * Appears only when user session is about to expire, this
 * component should be displayed and rendered early as possible in the DOM.
 * Component is built with `<TontineModal />`
 */
const SessionExtensionModal = () => {
  //hooks
  const t = useTranslate()
  const { isAuthenticated, send, context, currentState } = useAccountService()

  const isLoading = currentState === 'EXTENDING_SESSION'

  const [error, setError] = useState<ErrorStorage>()
  const [hidePinAuth, setHidePinAuth] = useState(false)

  //If the correct pin is provided to the server, it will respond with a new
  //auth_token so here we send the auth_token to the AuthMachine so it continues
  //checking session with the new auth token
  const handleOnSuccess = () => {
    setHidePinAuth(false)

    toast.success(
      <ToastMessage title={t('EXPIRE_SESSION_MODAL.EXTENDED_SESSION_TITLE')} />
    )
  }

  const extendFirstTimeSetup = () => {
    setHidePinAuth(true)
  }

  /**
   * Changes the modal content depending on how much has the user
   * completed the account setup process
   */
  const sessionModalExplainerText = () => {
    return t('COMMON.MODAL_LOGIN_ABOUT_TO_EXPIRE_ENTER_PIN')
  }

  const handleOnFailedPinSubmit = (error?: ErrorStorage) => setError(error)

  const handleCreatePin = (newPin: string) => {
    send({
      type: 'CREATE_NEW_PIN',
      payload: {
        pin: newPin,
        successCallback: extendFirstTimeSetup,
        failureCallback: handleOnFailedPinSubmit,
      },
    })
  }

  // Only executed if user wants to perform an action that requires for them to
  // enter pin and they don't have a pin, so they first set a pin and after
  // successfully setting a pin we call the API that requires pin authorization
  // with a freshly set pin
  const {
    setPin,
    pin: initialPin,
    handleSubmitPin,
  } = useSubmitPin({
    authMachineEvent: 'EXTEND_SESSION',
    successCallback: handleOnSuccess,
    failureCallback: handleOnFailedPinSubmit,
  })

  const handleChange = (pin: PinType) => {
    setPin(pin)
    handleSubmitPin({ pin: pin?.join('') })
  }

  /**
   * Renders pin setup component if the user has not set a pin
   */
  const renderModalContent = () => {
    if (!context?.user_details?.pin_set) {
      return (
        <SessionExtensionTimerAndTitle
          content={sessionModalExplainerText()}
          serverTime={context?.sessionAboutToExpire?.secondsRemaining}
        >
          <PinConfirmation
            handleSubmit={handleCreatePin}
            errorCallback={handleOnFailedPinSubmit}
          />
        </SessionExtensionTimerAndTitle>
      )
    }

    if (!hidePinAuth) {
      return (
        <SessionExtensionTimerAndTitle
          content={sessionModalExplainerText()}
          serverTime={context?.sessionAboutToExpire?.secondsRemaining}
        >
          <ConfirmationModal
            title={t('PIN_SUBMITTING_MESSAGE')}
            isOpen={isLoading}
          />
          <PinInput
            label={''}
            type={CONSTANTS.HIDE_PIN}
            errorMessage={error?.translatedError}
            values={initialPin}
            onChange={handleChange}
          >
            <TextError position="relative" errorText={error?.translatedError} />
          </PinInput>
        </SessionExtensionTimerAndTitle>
      )
    }

    return (
      <LottieAnimation
        animationName={ANIMATION.loadingLightBlueDots}
        autoplay
        loop
      />
    )
  }

  if (isAuthenticated) {
    return (
      <Modal
        isOpen={Boolean(context?.sessionAboutToExpire?.notifyUser)}
        backdrop
        dataTestID={UI_TEST_ID.sessionExtensionModal}
      >
        {renderModalContent()}
        <Button
          className={style['sessionExtensionModal__dismiss-button']}
          onClick={() => {
            send({
              type: 'SESSION_ABOUT_TO_EXPIRE',
              payload: {
                hasDeclinedSessionExtension: true,
              },
            })
          }}
        >
          {t('COMMON.CONTINUE_BUTTON')}
        </Button>
      </Modal>
    )
  }

  return <></>
}

export default SessionExtensionModal

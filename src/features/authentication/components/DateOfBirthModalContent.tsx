import Button from '../../../common/components/Button'
import DateOfBirthInput from '../../../common/components/DateOfBirthInput'
import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useEditDateOfBirth } from '../hooks/useEditDateOfBirth'
import { useRenderControl } from '../hooks/useRenderControl'
import style from '../style/DobModalContent.module.scss'
import { DateOfBirthModalContentParams } from '../types/DateOfBirth.type'

/**
 * Renders a date input component, where the user can enter their full
 * unverified date of birth. The content contains two steps, first step being an
 * text content explaining to the user why they need to add their date of birth,
 * and second step being the date input itself
 */
const DateOfBirthModalContent = ({
  onClickCloseButton,
  setShowCloseButton,
  title,
  addDobLabel,
  saveDobLabel,
  closeModalLabel,
  isLoadingPlanAndThresholds,
}: DateOfBirthModalContentParams): JSX.Element => {
  const t = useTranslate()

  const { dob, setDob, loading, saveDateOfBirth } = useEditDateOfBirth({
    onFailure: setShowCloseButton,
    onClickCloseButton,
    isLoadingPlanAndThresholds,
  })

  const { saveButtonLabel, saveButtonOnClick, closeModalButtonOnClick } =
    useRenderControl({
      showDobInput: false,
      saveDobLabel,
      addDobLabel,
      onClickCloseButton,
      onClickSaveButton: saveDateOfBirth,
    })

  return (
    <section className={style[`dob-modal-content`]}>
      <div className={style[`dob-modal-content__icon-container`]}>
        <Icon
          fileName={ASSET.bdayCake}
          className={style[`dob-modal-content__icon`]}
        />
      </div>
      <h3 className={style[`dob-modal-content__title-content`]}>{title}</h3>
      <DateOfBirthInput value={dob} onChange={setDob} />

      <div className={style[`dob-modal-content__btn-container`]}>
        <Button
          onClick={saveButtonOnClick}
          loading={loading}
          textOnLoading={t('LOADING_TEXT')}
        >
          {saveButtonLabel}
        </Button>

        <Button variant="secondary" onClick={closeModalButtonOnClick}>
          {closeModalLabel}
        </Button>
      </div>
    </section>
  )
}

export default DateOfBirthModalContent

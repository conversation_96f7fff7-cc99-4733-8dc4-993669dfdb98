import Card from '../../../common/components/card/Card'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { idVerificationAlertStatus } from '../../../common/utils/UtilFunctions'
import style from '../style/PersonalDetails.module.scss'
import { IDVerificationCardProps } from '../types/IDVerificationCard.types'
import { idVerificationStatusText } from '../utils/UtilsFunctions'

const IDVerificationCard = ({ status, onClick }: IDVerificationCardProps) => {
  const t = useTranslate()

  return (
    <div className={style['personalDetails__id-verify']}>
      <Card
        title={t('ID_VERIFY_TITLE')}
        subTitle={idVerificationStatusText(status)}
        headerImage={ASSET.iconaccountiderify}
        headerImageSize="large"
        variant="gray-dirty"
        showArrow={!!onClick}
        interactEnabled={!!onClick}
        alert={idVerificationAlertStatus(status)}
        onClick={onClick}
      />
    </div>
  )
}

export default IDVerificationCard

import { useState } from 'react'
import TontineModal from '../../../common/components/Modal'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { DateOfBirthModalProps } from '../types/DateOfBirth.type'
import DateOfBirthModalContent from './DateOfBirthModalContent'

/**
 * Renders a date of birth update modal, controls the hiding and showing of
 * modal close button
 */
const DateOfBirthModal = ({
  isOpen,
  setOpenDobModal,
  isLoadingPlanAndThresholds,
}: DateOfBirthModalProps) => {
  const t = useTranslate()
  const [showCloseButton, setShowCloseButton] = useState(false)

  return (
    <TontineModal
      isOpen={isOpen}
      backdrop
      onClickCloseButton={() => setOpenDobModal(false)}
      showCloseButton={showCloseButton}
    >
      <DateOfBirthModalContent
        title={t('DOB_MODAL_TITLE')}
        content={t('DOB_MODAL_CONTENT')}
        addDobLabel={t('DOB_MODAL.ADD_DOB_BTN')}
        saveDobLabel={t('DOB_MODAL.CONFIRM_DOB_BTN')}
        onClickCloseButton={() => setOpenDobModal(false)}
        setShowCloseButton={() => setShowCloseButton(true)}
        closeModalLabel={t('DOB_MODAL.CLOSE_DOB_BTN')}
        footerText={t('DOB_MODAL.FOOTER_TEXT')}
        isLoadingPlanAndThresholds={isLoadingPlanAndThresholds}
      />
    </TontineModal>
  )
}

export default DateOfBirthModal

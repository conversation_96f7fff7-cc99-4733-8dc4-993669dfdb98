import PropTypes from 'prop-types'
import MobileHeader from '../../../common/components/MobileHeader'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import style from '../style/AccountSummary.module.scss'
import AccountSummaryBody from './AccountSummaryBody'

/**
 * Displays's user's first, last name and email with the user's
 * initials on desktop devices, for mobile it renders the page title or if
 * `pageTitle` is not passed in renders greeting messages depending on which
 * part of the day it is
 */
const AccountSummary = ({ pageTitle }: { pageTitle?: string }) => {
  const { isMobileOrTablet } = useDeviceScreen()

  if (isMobileOrTablet) {
    return (
      <MobileHeader rootScreenHeader hideBackButton pageTitle={pageTitle} />
    )
  }

  return (
    <article className={style.accountSummary}>
      <AccountSummaryBody />
    </article>
  )
}

AccountSummary.propTypes = {
  pageTitle: PropTypes.string,
}

export default AccountSummary

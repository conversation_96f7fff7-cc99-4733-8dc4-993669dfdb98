import { UserDetails } from './AuthMachineTypes.type'

interface MenuItem {
  to: string
  mainText: string
  icon: string
  alertCount?: number
  writeProtected?: boolean
  dataTestID?: string
}

interface DividerItem {
  isDivider: true
}

type MenuGroupItem = MenuItem | DividerItem

interface MenuGroup {
  title: string
  items: Array<MenuGroupItem>
}

type MenuItemsFunction = (user_details?: UserDetails) => Array<MenuGroup>

export type {
  MenuItem,
  DividerItem,
  MenuGroupItem,
  MenuGroup,
  MenuItemsFunction,
}

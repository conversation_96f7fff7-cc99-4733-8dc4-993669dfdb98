type ScanRoutes = 'enroll-face' | 'match-id' | 'auth-scan'
type PageSetting = {
  pageTitle: string
  scanType: 'ENROLLMENT' | 'PHOTO_ID_SCAN' | 'AUTHENTICATION'
  scanError: string
}

type FaceScanProps = {
  email?: string
  onClickExitScan?: () => void
  asModal?: boolean
  scanType: ScanRoutes
  onSuccessfulScan?: () => void
  showMobileBar?: boolean
}

export type { ScanRoutes, PageSetting, FaceScanProps }

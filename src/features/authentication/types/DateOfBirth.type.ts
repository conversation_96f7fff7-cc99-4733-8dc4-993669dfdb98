type DateOfBirthModalProps = {
  isOpen: boolean
  setOpenDobModal: (isOpen: boolean) => void
  isLoadingPlanAndThresholds: boolean
}
type DateOfBirthModalContentParams = {
  onClickCloseButton: () => void
  setShowCloseButton?: () => void
  title: string
  content: string
  addDobLabel: string
  saveDobLabel: string
  closeModalLabel: string
  footerText: string
  isLoadingPlanAndThresholds: boolean
}

type RenderControlParams = {
  showDobInput: boolean
  saveDobLabel: string
  addDobLabel: string
  onClickCloseButton: () => void
  onClickSaveButton: () => void
}

type RenderControlReturn = {
  saveButtonLabel: string
  saveButtonOnClick: () => void
  closeModalButtonOnClick: () => void
}

type SaveChoiceAndChangeStateParams = {
  onClickCloseButton: () => void
  saveInLocalStorage: (dismissedModal: boolean) => void
}

export type {
  DateOfBirthModalProps,
  DateOfBirthModalContentParams,
  RenderControlParams,
  RenderControlReturn,
  SaveChoiceAndChangeStateParams,
}

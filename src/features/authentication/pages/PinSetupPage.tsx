import { useTranslate } from '../../../common/hooks/useTranslate'
import PinConfirmation from '../components/PinConfirmation'
import style from '../style/PinSetupPage.module.scss'

import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import Layout from '../../../common/components/Layout'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import { ANIMATION } from '../../../common/constants/Animations'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import { PUBLIC } from '../../../routes/Route'
import PinChange from '../components/PinChange'
import { useAccountService } from '../hooks/useAccountService'
import { usePinChange } from '../hooks/usePinChange'

/**
 * PinSetupPage
 *
 * This page is used to set up a PIN for the first time, or to change an existing PIN.
 * The page is a simple form with a title and a single input field for the PIN.
 */
const PinSetupPage = () => {
  const t = useTranslate()

  const {
    send,
    context: { user_details },
    currentState,
  } = useAccountService()

  // Loading state for Modal
  const isLoading =
    currentState === 'CHANGING_CURRENT_PIN' ||
    currentState === 'CREATING_NEW_PIN'

  const pinLength = CONSTANTS.PIN_INPUT_FIELDS

  const {
    error,
    oldPinRefs,
    newPinRefs,
    oldPin,
    handleSetOldPin,
    handleChangePin,
  } = usePinChange(pinLength)

  const handleCreatePin = (pin?: string) => {
    send({
      type: 'CREATE_NEW_PIN',
      payload: {
        pin,
      },
    })
  }

  return (
    <Layout
      containerWidth="small"
      pageTitle={
        user_details?.pin_set
          ? t('AUTH.PAGE_TITLE_CHANGE_PIN')
          : t('PIN_SETUP_TITLE')
      }
      navigateTo={PUBLIC.GO_TO_PREV_PAGE}
    >
      <ErrorBoundaryAndSuspense>
        <section className={style['pin-setup-page']}>
          {user_details?.pin_set && (
            <PinChange
              label={t('KYC.INPUT_LABEL_CONFIRM_OLD_PIN')}
              pinLength={pinLength}
              pin={oldPin}
              onChange={handleSetOldPin}
              error={error}
              pinChangeRefs={oldPinRefs}
            />
          )}

          <PinConfirmation
            headerTitle={
              user_details?.pin_set
                ? t('PIN_CHANGE.HEADER_TITLE')
                : t('PIN_SETUP.HEADER_TITLE')
            }
            initialPinLabel={t('PIN_PROMPT.PIN_INPUT_LABEL')}
            confirmPinLabel={t('AUTH.INPUT_LABEL_CONFIRM_PIN')}
            pinLength={pinLength}
            handleSubmit={
              user_details?.pin_set ? handleChangePin : handleCreatePin
            }
            externalInputRefs={newPinRefs}
            focusOnSuccess={false}
          />
          <ConfirmationModal
            animatedIcon={ANIMATION.loadingLightBlueDots}
            isOpen={isLoading}
            title={t('PIN_SUBMITTING_MESSAGE')}
          />
        </section>
      </ErrorBoundaryAndSuspense>
    </Layout>
  )
}

export default PinSetupPage

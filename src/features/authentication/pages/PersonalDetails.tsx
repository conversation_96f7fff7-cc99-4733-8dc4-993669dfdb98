import { useState } from 'react'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import Layout from '../../../common/components/Layout'
import TextError from '../../../common/components/TextError'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PRIVATE } from '../../../routes/Route'
import DiscardChangesModal from '../components/DiscardChangesModal'
import EditNavigationButtons from '../components/EditNavigationButtons'
import IDVerificationCard from '../components/IDVerificationCard'
import PersonalDetailsForm from '../components/PersonalDetailsForm'
import { useAccountService } from '../hooks/useAccountService'
import { useBiometricsControl } from '../hooks/useBiometricsControl'
import { useEditPersonalDetails } from '../hooks/useEditPersonalDetails'
import FaceScan from './FaceScan'

const PersonalDetails = () => {
  const t = useTranslate()
  const { isMobileOrTablet } = useDeviceScreen()
  const {
    context: { user_details },
  } = useAccountService()

  const [showDiscardModal, setShowDiscardModal] = useState(false)
  const [showFaceScan, setShowFaceScan] = useState(false)

  const {
    userIsTyping,
    editApiError,
    saveEditedDetails,
    loading,
    inputValidation,
    ...rest
  } = useEditPersonalDetails()

  const { safeToStartIDV, isReadOnly } = useBiometricsControl({
    userIsTyping,
    userIsTypingCallback: () => setShowDiscardModal(true),
    setOpenFaceScanModal: setShowFaceScan,
  })

  const cardNotClickable =
    user_details?.id_review_status === 'not_reviewed' ||
    user_details?.id_review_status === 'approved'

  return (
    <main>
      <DiscardChangesModal
        isOpen={showDiscardModal}
        onClose={() => setShowDiscardModal(false)}
        onSave={() => {
          saveEditedDetails(() => {
            setShowDiscardModal(false)
            setShowFaceScan(true)
          })
        }}
      />
      {showFaceScan && (
        <FaceScan
          asModal
          onClickExitScan={() => setShowFaceScan(false)}
          onSuccessfulScan={() => setShowFaceScan(false)}
          scanType="match-id"
        />
      )}

      <Layout
        pageTitle={t('PERSONAL_DETAILS.FORM_TITLE')}
        containerHeight={isMobileOrTablet ? 'lh' : 'sh'}
        navigateTo={PRIVATE.ACCOUNT}
        containerMt="nomt"
        hideMobileHeader={showFaceScan}
        card={
          <IDVerificationCard
            status={user_details?.id_review_status}
            onClick={cardNotClickable ? undefined : safeToStartIDV}
          />
        }
        bottomSection={
          !isReadOnly() ? (
            <EditNavigationButtons
              inputValidation={inputValidation}
              loading={loading}
              userIsTyping={userIsTyping}
              onSave={() => saveEditedDetails(() => setShowDiscardModal(false))}
            />
          ) : undefined
        }
      >
        <ErrorBoundaryAndSuspense>
          <PersonalDetailsForm
            {...rest}
            inputValidation={inputValidation}
            userIsTyping={userIsTyping}
            isReadOnly={isReadOnly()}
            navigateToBiometrics={() => setShowFaceScan(true)}
            saveEditedDetails={saveEditedDetails}
          />
        </ErrorBoundaryAndSuspense>

        {editApiError && <TextError errorText={editApiError} />}
      </Layout>
    </main>
  )
}

export default PersonalDetails

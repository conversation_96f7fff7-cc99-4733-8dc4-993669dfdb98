import { Trans } from 'react-i18next'
import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import style from '../style/ScanInstructions.module.scss'
import {
  ScanInstructionContentProps,
  ScanInstructionsProps,
} from '../types/ScanInstructions.type'

/** Renders a single instruction for the face scan modal. */
const ScanInstructionContent = ({
  fileName,
  title,
  text,
  subtext,
}: ScanInstructionContentProps) => (
  <div className="scanInstructions__title-block">
    <div className={style['scanInstructions__title-container']}>
      <Icon fileName={fileName} />
      <p>
        <Trans i18nKey={title} />
      </p>
    </div>
    <p>
      <Trans i18nKey={text} />
    </p>
    {subtext && (
      <p className="scanInstructions__subtext">
        <Trans i18nKey={subtext} />
      </p>
    )}
  </div>
)

/**
 * Scanning instructions for face biometrics, if there is a scanning error, the
 * component renders in error state with instructions to help the user correct
 * their scan error
 */
const ScanInstructions = ({
  scanningErrorMessage,
  title,
}: ScanInstructionsProps) => {
  return (
    <section className={style.scanInstructions}>
      <article className={style['scanInstructions__main-container']}>
        {title && !scanningErrorMessage && (
          <h2 className={style['scanInstructions__title']}>
            <Trans i18nKey={title} />
          </h2>
        )}
        {!scanningErrorMessage && (
          <p className={style['scanInstructions__explainer']}>
            <Trans i18nKey={'VERIFY_ACC_INSTRUCTIONS.SUBTITLE'} />
          </p>
        )}

        {title && scanningErrorMessage && (
          <h2 className={style['scanInstructions__title']}>{title}</h2>
        )}

        {scanningErrorMessage && (
          <>
            <Icon
              fileName={ASSET.infoamber}
              className={style['scanInstructions__error-icon']}
            />
            <h1 className={style['scanInstructions__title']}>
              {<Trans i18nKey={scanningErrorMessage} />}
            </h1>
          </>
        )}
        <div className={style['scanInstructions__requirements-container']}>
          <ScanInstructionContent
            fileName={ASSET.documentIcon}
            title={'VERIFY_ACC_INSTRUCTIONS.DOCUMENT_REQ_TITLE'}
            text={'VERIFY_ACC_INSTRUCTIONS.DOCUMENT_REQ_TEXT'}
            subtext={'VERIFY_ACC_INSTRUCTIONS.DOCUMENT_REQ_SUBTEXT'}
          />
          <ScanInstructionContent
            fileName={ASSET.cameraIcon}
            title={'VERIFY_ACC_INSTRUCTIONS.FACE_SCAN_REQ_TITLE'}
            text={'VERIFY_ACC_INSTRUCTIONS.FACE_SCAN_REQ_TEXT'}
          />
          <ScanInstructionContent
            fileName={ASSET.faceScanIcon}
            title={'VERIFY_ACC_INSTRUCTIONS.AFTER_VERIFICATION_REQ_TITLE'}
            text={'VERIFY_ACC_INSTRUCTIONS.AFTER_VERIFICATION_REQ_TEXT'}
          />
        </div>
      </article>
    </section>
  )
}

export default ScanInstructions

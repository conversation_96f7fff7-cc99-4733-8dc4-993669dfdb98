import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import Layout from '../../../common/components/Layout'
import MultiSelection from '../../../common/components/MultiSelection'
import PageContent from '../../../common/components/PageContent'
import ToastMessage from '../../../common/components/ToastMessage'
import { ASSET } from '../../../common/constants/Assets'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { formatDate } from '../../../common/utils/UtilFunctions'
import { ACCOUNT_MENU, PRIVATE } from '../../../routes/Route'
import CloseAccountButtons from '../components/CloseAccountButtons'
import { useAccountService } from '../hooks/useAccountService'
import { useSubmitPin } from '../hooks/useSubmitPin'
import PinAuthorization from './PinAuthorization'

/**
 * Renders an account closing flow where the user can close their
 * account, and if their account is scheduled for closure then a page will be
 * displayed here to inform the user about the scheduled closure and a chance to
 * reactivate their account
 */
const CloseAccount = () => {
  //Hooks
  const { isMobileOrTablet } = useDeviceScreen()
  const t = useTranslate()
  const {
    context: { user_details },
  } = useAccountService()

  const feedbackOptions = [
    {
      id: 1,
      title: t('ACCOUNT.DELETE_ACCOUNT_CHECKBOX_1'),
    },
    {
      id: 2,
      title: t('ACCOUNT.DELETE_ACCOUNT_CHECKBOX_2'),
    },
    {
      id: 3,
      title: t('ACCOUNT.DELETE_ACCOUNT_FEEDBACK_CHECKBOX_3'),
    },
    {
      id: 5,
      title: t('ACCOUNT.DELETE_ACCOUNT_FEEDBACK_CHECKBOX'),
    },
  ]

  //States
  //When the CLOSE ACCOUNT button is clicked the 2nd time
  const [confirmClosing, setConfirmClosing] = useState(false)
  //State for the multi select component
  const [beforeClosingAccountFeedback, setBeforeClosingAccountFeedback] =
    useState({
      activeSelection: null,
      data: feedbackOptions,
    })
  const [pinAuthorization, setPinAuthorization] = useState(false)
  //Feedback from the feedback options
  const [closeAccountFeedback, setCloseAccountFeedback] = useState([])
  //When (other) option is selected this feedback will be prioritized
  const [closeAccountAdditionalFeedback, setCloseAccountAdditionalFeedback] =
    useState(null)
  //When the account is reactivated, changes the content of the component
  const [reactivatedAccount, setReactivatedAccount] = useState(false)
  //API states
  const [error, setError] = useState(null)

  //Checks if other feedback is selected so a textarea can be rendered
  const renderTextAreaForOtherFeedback = () => {
    if (
      !user_details?.closure_scheduled_time &&
      !reactivatedAccount &&
      confirmClosing
    ) {
      return closeAccountFeedback?.includes(feedbackOptions[3].title)
    }
  }

  /**
   * @description Chooses what type of feedback will be sent to the server, if
   * there is feedback from feedback options send that feedback, if there is
   * additional feedback, send that feedback otherwise send nothing
   */
  const chooseFeedback = () => {
    if (closeAccountFeedback) {
      return `${closeAccountFeedback.toString()}, User comment:${closeAccountAdditionalFeedback}`
    }

    //Needs to be an empty space because the server expect a null or string, and
    //if null is send the `<PinAuthorization />` will treat it as an empty body
    //request
    return ' '
  }

  /**
   * @description Changes the main text content when the user proceeds with
   * closing their account
   */
  const mainTextContent = () => {
    //Puts the component in a closed account state
    //if there is closure scheduled time, then the account
    //is scheduled for closure
    if (user_details?.closure_scheduled_time) {
      return 'CLOSE_ACCOUNT.CLOSED_ACCOUNT_CONTENT'
    }

    //Account has been reactivated and the user_details?.closure_scheduled_time
    //has been wiped from context, so temporary success content will be displayed
    if (reactivatedAccount) {
      return 'CLOSE_ACCOUNT.REACTIVATED_ACCOUNT_CONTENT'
    }

    //Controls the two parts of closing the account
    //first part is the warning message, 2nd part is rendered when the
    //user clicks the CLOSE ACCOUNT button and feedback options are rendered
    return confirmClosing
      ? 'DELETE_ACCOUNT.FEEDBACK'
      : 'DELETE_ACCOUNT.CONFIRM_INFO'
  }

  /**
   *
   * @param {string} feedback Adds feedback to the `closeAccountFeedback` state,
   * if the same feedback is passed in it is removed from the state, same as
   * checkbox behavior
   */
  const addCloseAccountFeedback = (feedback) => {
    //Checks if the selected feedback is already added, if it is already added
    //then remove it, because that is how checkbox behavior is simulated
    if (closeAccountFeedback.includes(feedback?.title)) {
      setCloseAccountFeedback((prevState) =>
        prevState.filter((currentReason) => currentReason !== feedback?.title)
      )
    } else {
      setCloseAccountFeedback((prevState) => [...prevState, feedback?.title])
    }
  }

  /**
   * //FIXME: React anti-patter if a function returns jsx it should be a component!!
   *
   * @description Changes the main page's to a feedback content if the user has
   * proceeded with closing their account
   */
  const mainPageContent = () => {
    //Account has been confirmed for closing and has not been closed then
    //display a feedback options to the user to start the closing process
    if (
      //If account is confirmed for closing and does not have a closure
      //scheduled time and has not been reactivated, then display the feedback options
      confirmClosing &&
      !user_details?.closure_scheduled_time &&
      !reactivatedAccount
    ) {
      return (
        <MultiSelection
          setMultiSelectionData={setBeforeClosingAccountFeedback}
          multiSelectionData={beforeClosingAccountFeedback}
          onSelection={addCloseAccountFeedback}
          allowMultipleItems
        />
      )
    }
  }

  /**
   * @description Changes the icon if the user has proceeded with closing their
   * account
   */
  const showIcon = () => {
    //Account has been scheduled for closure display icon for
    //that state
    if (user_details?.closure_scheduled_time) {
      return ASSET.iconaccountmenucontributionhitory
    }

    //Account has been reactivated display icon for that state
    if (reactivatedAccount) {
      return ASSET.iconaccountcelebratin
    }

    //Account has been confirmed for closing display icon for that state
    //otherwise display the default icon
    return confirmClosing ? ASSET.iconnotlikelyemojiurey : ASSET.infoamber
  }

  /**
   * @description Changes the handler for the button if the user has proceeded
   * with closing their account
   */
  const onClickCloseAccount = () => {
    if (confirmClosing) {
      setPinAuthorization(true)
    } else {
      setConfirmClosing(true)
    }
  }

  const onSuccessfulAccountScheduling = () => {
    //Close the pin authorization page
    setPinAuthorization(false)

    //Display to the user a success toast message
    toast.success(
      <ToastMessage title={t('CLOSE_ACCOUNT.SCHEDULED_SUCCESS_CONTENT')} />
    )
  }

  const onFailedAccountScheduling = (error) => {
    setError(error?.translatedError)
    toast.error(<ToastMessage title={error?.translatedError} />)
  }

  useEffect(() => {
    if (!confirmClosing) {
      setCloseAccountFeedback([])
    }
  }, [confirmClosing])

  const { setPin, pin, handleSubmitPin } = useSubmitPin({
    authorizedData: chooseFeedback(),
    authMachineEvent: 'CLOSE_USER_ACCOUNT',
    successCallback: onSuccessfulAccountScheduling,
    failureCallback: onFailedAccountScheduling,
  })

  const handlePinChange = (pin) => {
    setPin(pin)
    handleSubmitPin({
      pin: pin.join(''),
      payload: {
        closureFeedback: chooseFeedback(),
      },
    })
  }

  if (pinAuthorization) {
    return (
      <PinAuthorization
        onClickAction={() => setPinAuthorization(false)}
        pageTitle={t('ACCOUNT.PAGE_TITLE_DELETE_ACCOUNT')}
        navigateTo={ACCOUNT_MENU.CLOSE_ACCOUNT}
        mainTitle={t('ACCOUNT.CLOSING_ACCOUNT_TEXT')}
        errorMessage={error}
        authorizedData={chooseFeedback()}
        onSuccessfulPinSubmit={onSuccessfulAccountScheduling}
        onFailedPinSubmit={onFailedAccountScheduling}
        authMachineEvent={'CLOSE_USER_ACCOUNT'}
        loadingType={'CLOSING_USER_ACCOUNT'}
        setPin={handlePinChange}
        pin={pin}
      />
    )
  }

  return (
    <Layout
      containerWidth="small"
      onClickAction={
        isMobileOrTablet
          ? null
          : () => {
              setConfirmClosing(false)
            }
      }
      pageTitle={t('ACCOUNT.PAGE_TITLE_DELETE_ACCOUNT')}
      navigateTo={PRIVATE.ACCOUNT}
      bottomSection={
        <CloseAccountButtons
          confirmClosing={confirmClosing}
          reactivatedAccount={reactivatedAccount}
          setConfirmClosing={setConfirmClosing}
          scheduledClosingTime={user_details?.closure_scheduled_time}
          setReactivatedAccount={setReactivatedAccount}
          onClickCloseAccount={onClickCloseAccount}
        />
      }
    >
      <PageContent
        icon={showIcon()}
        locizeKey={mainTextContent()}
        mainContent={mainPageContent()}
        closedAccountButtons={Boolean(user_details?.closure_scheduled_time)}
        reactivateButtonLabel={t('CLOSE_ACCOUNT.REACTIVATE_BUTTON_LABEL')}
        textArea={renderTextAreaForOtherFeedback()}
        setTextAreaFeedback={setCloseAccountAdditionalFeedback}
        values={{
          scheduledTimeForClosing: formatDate(
            user_details?.closure_scheduled_time,
            'HH:mm:ss, MMM D YYYY'
          ),
        }}
        value={closeAccountAdditionalFeedback}
      />
    </Layout>
  )
}

export default CloseAccount

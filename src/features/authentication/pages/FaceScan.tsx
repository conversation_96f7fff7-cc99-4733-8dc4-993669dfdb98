import { useState } from 'react'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import Layout from '../../../common/components/Layout'
import TontineModal from '../../../common/components/Modal'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { ASSET } from '../../../common/constants/Assets'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { PUBLIC } from '../../../routes/Route'
import PermissionModal from '../components/PermissionModal'
import { useFaceScan } from '../hooks/useFaceScan'
import style from '../style/FaceScan.module.scss'
import { FaceScanProps } from '../types/FaceScan.type'
import ScanInstructions from './ScanInstructions'

/**
 * Handles the face scan and changes the content depending on which type
 * of scan the user is doing
 *
 * - Handles browser permissions
 * - Starts a biometrics facescan
 * - Provides scan guidance (more design work is needed)
 *
 * @note For enrolling use `match-id` where enrolling and IDV is merged
 * into one flow!
 */
const FaceScan = ({
  email,
  onClickExitScan,
  asModal,
  scanType,
  onSuccessfulScan,
  showMobileBar,
}: FaceScanProps) => {
  const { isMobileOrTablet } = useDeviceScreen()
  const {
    t,
    startScan,
    scanIsInProgress,
    scanError,
    navigate,
    pageSetting,
    scriptLoaded,
  } = useFaceScan({
    email,
    scanType,
  })
  const [permissionError, setPermissionError] = useState<
    { error: string } | undefined
  >(undefined)
  const [isOpenScanModal, setIsOpenScanModal] = useState(Boolean(asModal))

  const startScanWithModal = (asModal: boolean) => {
    if (asModal) {
      startScan(({ idScanCompleted, enrollmentCompleted }) => {
        // Only close the modal if the whole math-id has been completed
        if (scanType === 'match-id' && idScanCompleted && enrollmentCompleted) {
          setIsOpenScanModal(false)
          onSuccessfulScan?.()
        }
        if (scanType === 'auth-scan' || scanType === 'enroll-face') {
          setIsOpenScanModal(false)
          onSuccessfulScan?.()
        }
      })
    } else {
      //TODO: Scan as a page, might need to add callbacks when that is the case
      startScan()
    }
  }

  const scanInstructions = (
    <ErrorBoundaryAndSuspense>
      <main className={style[`face-scan__scanInstructions`]}>
        {scriptLoaded && (
          <PermissionModal
            icon={ASSET.infoCircle}
            title={t('FACE_SCAN_PERMISSION.TITLE')}
            content={t('FACE_SCAN_PERMISSION.CONTENT')}
            onPermissionGranted={() => {
              startScanWithModal(asModal ?? false)
              setPermissionError(undefined)
            }}
            onPermissionDenied={() => {
              // Denied permissions render a explanation modal
              setPermissionError({
                error: 'NO_CAM_PERMISSIONS',
              })
            }}
            permission="video"
            askOnMount
          />
        )}

        <ScanInstructions
          title={
            asModal && !scanError && !permissionError ? 'ID_VERIFY_TITLE' : ''
          }
          scanningErrorMessage={
            permissionError ? permissionError?.error : scanError
          }
        />
        <NavigationButtons
          onClickFirst={
            onClickExitScan
              ? onClickExitScan
              : () => navigate(PUBLIC.GO_TO_PREV_PAGE)
          }
          disabledFirst={scanIsInProgress || !scriptLoaded}
          onClickSecond={() => startScanWithModal(asModal ?? false)}
          disabledSecond={Boolean(permissionError?.error)}
          secondButtonLabel={t('VERIFY_ACC_INSTRUCTIONS.JOIN_BUTTON_TEXT')}
          secondButtonLoading={scanIsInProgress || !scriptLoaded}
        />
      </main>
    </ErrorBoundaryAndSuspense>
  )

  if (asModal) {
    const modalInstructions = (
      <TontineModal
        isOpen={isOpenScanModal}
        wide={true}
        backdrop
        backdropType={isMobileOrTablet ? 'full-color' : undefined}
      >
        {scanInstructions}
      </TontineModal>
    )
    if (isMobileOrTablet) {
      return (
        // Layout needs to be added in mobile view only in order for the modal
        // to overlay the mobile nav bar
        <Layout
          onClickAction={onClickExitScan}
          hideMobileHeader={!showMobileBar}
        >
          {modalInstructions}
        </Layout>
      )
    }

    return modalInstructions
  }

  return (
    <Layout
      onClickAction={
        onClickExitScan
          ? onClickExitScan
          : () => navigate(PUBLIC.GO_TO_PREV_PAGE)
      }
      pageTitle={t(pageSetting?.pageTitle)}
    >
      {scanInstructions}
    </Layout>
  )
}

export default FaceScan

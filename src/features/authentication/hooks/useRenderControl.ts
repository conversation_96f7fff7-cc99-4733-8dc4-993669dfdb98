import { CONSTANTS } from '../../../common/constants/ConstantValues'
import useBrowserStorage from '../../../common/hooks/useBrowserStorage'
import {
  RenderControlParams,
  RenderControlReturn,
} from '../types/DateOfBirth.type'
import { saveChoiceAndChangeState } from '../utils/UtilsFunctions'

/**
 * Controls the labels and what should be rendered to the user as they progress
 * through the modal
 */
export const useRenderControl = ({
  addDobLabel,
  onClickCloseButton,
  onClickSaveButton,
}: RenderControlParams): RenderControlReturn => {
  const { addValueToStorage } = useBrowserStorage({
    key: CONSTANTS.DOB_MODAL_KEY,
  })

  /**
   * If Date input is rendered on screen then this function will call the
   * `onClickSaveButton` function passed in to the hook
   */
  const onClickSave = (): void => onClickSaveButton?.()

  return {
    saveButtonLabel: addDobLabel,
    saveButtonOnClick: onClickSave,
    closeModalButtonOnClick: () =>
      saveChoiceAndChangeState({
        onClickCloseButton,
        saveInLocalStorage: addValueToStorage,
      }),
  }
}

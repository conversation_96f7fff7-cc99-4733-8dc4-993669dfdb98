import { CONSTANTS } from '../../../common/constants/ConstantValues'
import useBrowserStorage from '../../../common/hooks/useBrowserStorage'
import { useUserInfo } from './FormHooks'

/**
 * Controls the session storage to persist the unverified phone number during verification
 */
export const usePhoneInputController = () => {
  const { storedValue, removeValueFromStorage } = useBrowserStorage({
    key: CONSTANTS.PHONE_ADDED_KEY,
    storageType: 'session',
  })

  const {
    phoneNumber: primaryPhoneNumber,
    setPhoneNumber: setPrimaryPhoneNumber,
  } = useUserInfo()

  return {
    unverifiedPhoneNumFromSs: storedValue,
    unverifiedPhone: primaryPhoneNumber,
    setUnverifiedPhone: setPrimaryPhoneNumber,
    removePersistedPhoneNumber: () =>
      removeValueFromStorage(CONSTANTS.PHONE_ADDED_KEY),
  }
}

import { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import ToastMessage from '../../../common/components/ToastMessage'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { useBankingService } from '../../banking/hooks/useBankingService'
import { useAccountService } from './useAccountService'

/**
 * Return type of the `useEditDateOfBirth` hook
 */
type UseEditDateOfBirthServices = {
  dob: string
  loading: boolean
  setDob: (value: string) => void
  saveDateOfBirth: () => void
}

/**
 * Makes an API request to `user_account/add_dob` endpoint and updates the auth
 * machine context if the new DoB has been added successfully
 */
const useEditDateOfBirth = ({
  onClickCloseButton,
  onFailure,
  isLoadingPlanAndThresholds,
}: {
  onClickCloseButton: () => void
  onFailure?: () => void
  isLoadingPlanAndThresholds: boolean
}): UseEditDateOfBirthServices => {
  const { sendBankEvent } = useBankingService()
  const t = useTranslate()
  const {
    send,
    context: { user_details },
  } = useAccountService()

  const [isLoading, setIsLoading] = useState(false)
  const [hasSetDoB, setHasSetDoB] = useState(false)

  const [dob, setDob] = useState<string>(user_details?.date_of_birth ?? '')

  const handleSuccess = (): void => {
    setHasSetDoB(true)
    sendBankEvent({
      type: 'GET_RETURNS',
    })
  }

  const handleFailure = (): void => {
    setIsLoading(false)
    onFailure?.()
    toast.error(<ToastMessage title={t('ERROR_GENERIC')} />)
  }

  /**
   * Sets the loading state to true and does an API request
   */
  const saveDateOfBirth = () => {
    setIsLoading(true)
    send({
      type: 'UPDATE_ACCOUNT_INFO',
      payload: {
        date_of_birth: dob,
        unverified_date_of_birth_set_by_user: Boolean(dob),
        successCallback: handleSuccess,
        failureCallback: handleFailure,
      },
    })
  }

  //HACK: This very bad solution is used to make sure the DoB modal does not close
  // before the UI has been updated with the new age threshold and draft plan
  // values so the user does not see the IncomeScheduler slider move back and forth
  useEffect(() => {
    if (hasSetDoB && isLoadingPlanAndThresholds) {
      toast.success(
        <ToastMessage title={t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT')} />
      )
      onClickCloseButton?.()

      // Re-set state in order for the user to set their DoB again
      // even tho this is not needed since it is one time thing anyway to set
      // your "accurate unverified" DoB as per
      // the design
      setHasSetDoB(false)

      setIsLoading(false)
    }
  }, [hasSetDoB, isLoadingPlanAndThresholds])

  return {
    dob,
    setDob,
    loading: isLoading,
    saveDateOfBirth,
  }
}

export { useEditDateOfBirth }

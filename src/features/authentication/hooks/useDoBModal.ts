import { useState } from 'react'
import { CONSTANTS } from '../../../common/constants/ConstantValues'
import useBrowserStorage from '../../../common/hooks/useBrowserStorage'
import { showDoBModal } from '../../../common/utils/UtilFunctions'
import { useAccountService } from './useAccountService'

/**
 * Controls whether the Date of birth modal should be open automatically for
 * fresh user accounts.
 */
export const useDoBModal = () => {
  const {
    context: { user_details },
  } = useAccountService()

  //Keeps track if the user has already closed the date of birth modal once
  const { storedValue: userClosedModalOnce } = useBrowserStorage({
    key: CONSTANTS.DOB_MODAL_KEY,
  })

  //Modal state
  const [openDobModal, setOpenDobModal] = useState<boolean | undefined>(
    showDoBModal({
      userClosedModalOnce: <PERSON><PERSON><PERSON>(userClosedModalOnce),
      hasUnverifiedOrVerifiedDoB: <PERSON><PERSON><PERSON>(
        user_details?.unverified_date_of_birth_set_by_user
      ),
      verifiedDoB: <PERSON><PERSON><PERSON>(user_details?.verified_date_of_birth),
    })
  )

  return {
    openDobModal,
    setOpenDobModal,
  }
}

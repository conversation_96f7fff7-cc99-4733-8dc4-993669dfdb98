import { TontinatorParamsMode } from '../../../common/types/CommonTypes.types'

/**
 * Specific initial values only to the tontinator flow
 */
const CALCULATOR_FORMS = {
  DEFAULT_SHOW_VALUES: {
    infoForm: false,
    sexForm: true,
    ageForm: false,
    contributionForm: false,
    forecast: false,
  },

  GRAPH_TOGGLES_DEFAULT_VALUES: {
    breakeven: false,
    inflation: false,
    percentage: false,
  },
}

const MAIN_SVG_ID = 'tontinator-svg-container'
const X_AXIS_CSS_CLASS = 'x-axis-tontinator-age'
const Y_AXIS_CSS_CLASS = 'y-axis-tontinator-payouts'
const CONTAINER_CSS_CLASS = 'forecast-graph-container'

const LINE_KEYS = {
  tontineLineKey: `line-tontine`,
  fixedAnnuityLineKey: `fixed-annuity`,
  bankDepositKey: `bank-deposit`,
}

const productToLabel = {
  IRA: 'PARAM_MODE_IRA',
  TTF: 'PARAM_MODE_TTF',
}

const legendKeys = {
  TTF: {
    FII: {
      tontine: `TTF.FII.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `TTF.FII.GRAPH_LEGEND.DEPOSIT`,
    },
    BOL: {
      tontine: `TTF.BOL.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `TTF.BOL.GRAPH_LEGEND.DEPOSIT`,
    },
    VBI: {
      tontine: `TTF.VBI.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `TTF.VBI.GRAPH_LEGEND.DEPOSIT`,
    },
    BTC: {
      tontine: `TTF.BTC.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `TTF.BTC.GRAPH_LEGEND.DEPOSIT`,
    },
    XAU: {
      tontine: `TTF.XAU.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `TTF.XAU.GRAPH_LEGEND.DEPOSIT`,
    },
  },
  IRA: {
    FII: {
      tontine: `IRA.FII.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `IRA.FII.GRAPH_LEGEND.DEPOSIT`,
    },
    BOL: {
      tontine: `IRA.BOL.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `IRA.BOL.GRAPH_LEGEND.DEPOSIT`,
    },
    VBI: {
      tontine: `IRA.VBI.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `IRA.VBI.GRAPH_LEGEND.DEPOSIT`,
    },
    BTC: {
      tontine: `IRA.BTC.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `IRA.BTC.GRAPH_LEGEND.DEPOSIT`,
    },
    XAU: {
      tontine: `IRA.XAU.GRAPH_LEGEND.TONTINE`,
      annuity: `GRAPH_LEGEND.ANNUITIES_BAR`,
      deposit: `IRA.XAU.GRAPH_LEGEND.DEPOSIT`,
    },
  },
}

const paramModeToLabel: Record<
  TontinatorParamsMode,
  {
    retirementAgeSliderLabel: string
    currentAgeSliderLabel: string
    disabledMonthlySliderLabel: string
    disableRetirementSliderLabel: string
    oneTimeContributionLabel: string
    monthlyContributionLabel: string
    investmentStrategyLabel: string
  }
> = {
  TTF: {
    retirementAgeSliderLabel: 'DESIRED_GOAL.TTF_RETIREMENT_AGE',
    currentAgeSliderLabel: 'INPUT_LABEL.TTF_CURRENT_AGE',
    disabledMonthlySliderLabel: 'TTF_DISABLED_MONTHLY_SLIDER_TEXT',
    disableRetirementSliderLabel: 'TTF_DISABLED_RETIREMENT_AGE_SLIDER',
    oneTimeContributionLabel: `EU_CONTRIBUTION_SLIDER.ONE_TIME`,
    monthlyContributionLabel: `TTF.CONTRIBUTION_SLIDER.MONTHLY`,
    investmentStrategyLabel: 'TTF.INVESTMENT_STRATEGY_DROPDOWN_LABEL',
  },
  IRA: {
    retirementAgeSliderLabel: 'DESIRED_GOAL.RETIREMENT_AGE',
    currentAgeSliderLabel: 'INPUT_LABEL.CURRENT_AGE',
    disabledMonthlySliderLabel: 'DISABLED_MONTHLY_SLIDER_TEXT',
    disableRetirementSliderLabel: 'DISABLED_RETIREMENT_AGE_SLIDER',
    oneTimeContributionLabel: `CONTRIBUTION_SLIDER.ONE_TIME`,
    monthlyContributionLabel: `CONTRIBUTION_SLIDER.MONTHLY`,
    investmentStrategyLabel: 'INVESTMENT_STRATEGY_DROPDOWN_LABEL',
  },
}

export {
  CALCULATOR_FORMS,
  CONTAINER_CSS_CLASS,
  legendKeys,
  LINE_KEYS,
  MAIN_SVG_ID,
  paramModeToLabel,
  productToLabel,
  X_AXIS_CSS_CLASS,
  Y_AXIS_CSS_CLASS,
}

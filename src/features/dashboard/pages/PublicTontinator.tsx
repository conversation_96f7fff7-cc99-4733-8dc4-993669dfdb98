import { useState } from 'react'
import Icon from '../../../common/components/Icon'
import { ASSET } from '../../../common/constants/Assets'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import {
  IncomeForecastParams,
  InvestmentStrategyId,
} from '../../../common/types/CommonTypes.types'
import { InvestmentDetails } from '../../authentication/types/AuthMachineTypes.type'
import { useBankingService } from '../../banking/hooks/useBankingService'
import LiteReferralLayout from '../../referral/components/LiteReferralLayout'
import BottomCtaLiteLayout from '../components/BottomCtaLiteLayout'
import ParamModes from '../components/ParamModes'
import PensionPlanDashboard from '../components/PensionPlanDashboard'
import TontinatorDashboard from '../components/TontinatorDashboardV2'
import {
  chooseDefaultParams,
  modifyParamsForComparison,
} from '../hooks/usePreRegisterForecast'
import style from '../style/PublicTontinatorPage.module.scss'
import { PublicTontinatorProps } from '../types/PublicTontinator.types'
import PublicTontinatorInputLayout from './PublicTontinatorInputLayout'

const idToProd = {
  TTF: 'TontineTrustFund',
  IRA: 'TontineIRA',
}

/**
 * Tontinator page for public users
 */
const PublicTontinator = ({
  incomeForecastParams,
  setIncomeForecastParams,
  setRegisterForm,
  blueForecastParams,
  setBlueForecastParams,
  yellowForecastParams,
  setYellowForecastParams,
  setIsOpenSignInModal,
}: PublicTontinatorProps) => {
  const { sendBankEvent } = useBankingService()
  const [isCompareOpen, setIsCompareOpen] = useState(false)
  const [openSliderPage, setOpenSliderPage] = useState(false)
  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode: incomeForecastParams?.countryOfResidence,
  })

  return (
    <main className={style['public-tontinator-page']}>
      {isCompareOpen && (
        <Icon
          // Top back button only on mobile
          fileName={ASSET.icononboardinarrowback}
          className={style['public-tontinator-page__back-mobile']}
          onClick={() => setIsCompareOpen(false)}
        />
      )}
      <section
        className={style['public-tontinator-page__tontinator-container']}
      >
        <section className={style['public-tontinator-page__chart-layout']}>
          <ParamModes
            onChange={(value) => {
              sendBankEvent({
                type: 'UPDATE_PRODUCT',
                payload: {
                  product: idToProd[value],
                },
              })
              const defaultParams = chooseDefaultParams({
                tontinatorParams: supportedCountry.tontinatorParams,
                supportedCountry,
              })

              setIncomeForecastParams({
                // reset to default params when the user changes the product
                // also known as params mode
                ...defaultParams,
                paramsMode: value,
              } as IncomeForecastParams)

              setBlueForecastParams({
                ...defaultParams,
                paramsMode: value,
              } as IncomeForecastParams)

              setYellowForecastParams({
                ...modifyParamsForComparison({
                  retirementAge: defaultParams?.retirementAge,
                  targetValue:
                    supportedCountry?.tontinatorParams?.maxRetirementAge?.age ??
                    0,
                  targetIncrement: 5,
                  strategies:
                    supportedCountry?.supportedInvestments as InvestmentDetails,
                  strategy: defaultParams?.strategy as InvestmentStrategyId,
                }),
                paramsMode: value,
              } as IncomeForecastParams)
            }}
            activeMode={incomeForecastParams.paramsMode ?? 'TTF'}
          />
          {isCompareOpen ? (
            <PensionPlanDashboard
              dataToDraw={[blueForecastParams, yellowForecastParams]}
            />
          ) : (
            <TontinatorDashboard incomeForecastParams={incomeForecastParams} />
          )}
        </section>

        <PublicTontinatorInputLayout
          isSliderPageOpen={openSliderPage}
          setOpenSliderPage={setOpenSliderPage}
          setRegisterForm={setRegisterForm}
          incomeForecastParams={incomeForecastParams}
          setIncomeForecastParams={setIncomeForecastParams}
          comparison={isCompareOpen}
          setIsCompareOpen={setIsCompareOpen}
          blueForecastParams={blueForecastParams}
          setBlueForecastParams={setBlueForecastParams}
          yellowForecastParams={yellowForecastParams}
          setYellowForecastParams={setYellowForecastParams}
          setIsOpenSignInModal={setIsOpenSignInModal}
          isCompareOpen={isCompareOpen}
        />
      </section>

      <section
        className={style['public-tontinator-page__bottom-cta-container']}
      >
        {!openSliderPage && (
          // Prevents a double render of bottom layout if the slider page is open
          <BottomCtaLiteLayout
            incomeForecastParams={incomeForecastParams}
            isSliderPageOpen={openSliderPage}
            onClickBack={() => setIsCompareOpen(false)}
            setIsOpenSignInModal={setIsOpenSignInModal}
            onClickSignUpButton={() => setRegisterForm(true)}
            onClickMobileTopButtonOnly={() =>
              setOpenSliderPage((prev) => !prev)
            }
            onClickComparison={() => setIsCompareOpen(true)}
            isCompareOpen={isCompareOpen}
            onClickPlan1={() => {
              setIncomeForecastParams(blueForecastParams)
              setIsCompareOpen(false)
            }}
            onClickPlan2={() => {
              setIncomeForecastParams(yellowForecastParams)
              setIsCompareOpen(false)
            }}
            blueForecastParams={blueForecastParams}
            yellowForecastParams={yellowForecastParams}
          />
        )}

        {!isCompareOpen && <LiteReferralLayout />}
      </section>
    </main>
  )
}

export default PublicTontinator

import Header from '../../../common/components/Header'
import MobileAppBar from '../../../common/components/MobileAppBar'
import NavigationCard from '../../../common/components/NavigationCard'
import PageLayout from '../../../common/components/PageLayout'
import { ASSET } from '../../../common/constants/Assets'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { renderNominalBalance } from '../../../common/utils/UtilFunctions'
import { DASHBOARD_NAVIGATION, MYTT_DASHBOARD } from '../../../routes/Route'
import AccountSummary from '../../authentication/components/AccountSummary'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useBankingService } from '../../banking/hooks/useBankingService'
import NextPayoutCard from '../../dashboard/components/NextPayoutCard'
import UserFeedback from '../../feedback/components/UserFeedback'
import { chooseDefaultParams } from '../hooks/usePreRegisterForecast'
import style from '../style/MyTontineMobileDashboard.module.scss'
import FundedDashboard from './FundedDashboard'

/**
 * Renders the MyTontine dashboard
 */
const MyTontineDashboard = () => {
  const t = useTranslate()
  const {
    context: { user_details },
  } = useAccountService()
  const { formatAmount } = useLocalization()
  const { bankContext } = useBankingService()
  const { detectedCountry } = useLocalization()
  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode: detectedCountry?.alpha3,
  })
  const { tontinatorParams } = supportedCountry

  const defaultParams = chooseDefaultParams({
    tontinatorParams,
    supportedCountry,
  })

  return (
    <>
      <AccountSummary />
      <main className={style.myTontineDashboard}>
        <PageLayout containerHeight="lite-build" containerMt="nomt">
          <section className={style['myTontineDashboard__top-section']}>
            {user_details?.kyc_status?.L2?.passed_level && (
              <NextPayoutCard className={style['myTontineDashboard__card']} />
            )}

            <Header
              className={style['myTontineDashboard__header']}
              title={t('DASHBOARD.CHART.TITLE.EXPECTED.MONTHLY.INCOME')}
            />
          </section>

          <FundedDashboard
            data={defaultParams}
            error={null}
            isLoading={false}
          />

          <section className={style['myTontineDashboard__bottom-section']}>
            <NavigationCard
              headerImage={ASSET.iconmtnominalbalanceUS}
              subTitle={t('NOMINAL_BALANCE_LABEL')}
              title={
                renderNominalBalance(formatAmount, bankContext, 'standard')
                  ?.formattedAmountWithSymbol ?? '-'
              }
              navigateTo={DASHBOARD_NAVIGATION.NOMINAL_BALANCE}
              showArrow
              variant="gray-dirty"
            />

            <NavigationCard
              title={t('MYTT_DASHBOARD.CARD_EDS_TITLE')}
              subTitle={t('EXPLORE.DIFFERENT.SCENARIOS.DASHBOARD.SUB')}
              headerImage={ASSET.iconmttontinator}
              navigateTo={DASHBOARD_NAVIGATION.TONTINATOR}
              showArrow
              variant="gray-dirty"
            />

            <NavigationCard
              title={t('INVITE_FRIENDS.GIVE_GET_STATEMENT')}
              subTitle={t('INVITE_FRIENDS.WITH_FRIENDS_CAPTION')}
              headerImage={ASSET.iconaccountrewardbicolor}
              navigateTo={DASHBOARD_NAVIGATION.DASHBOARD_REWARDS}
              showArrow
              variant="gray-dirty"
            />

            <NavigationCard
              headerImage={ASSET.blog}
              title={t('DASHBOARD.NEWS_FEED_TITLE')}
              subTitle={t('DASHBOARD.CARD_NEWS_FEED_TEXT')}
              navigateTo={MYTT_DASHBOARD.NEWS_FEED}
              showArrow
              variant="gray-dirty"
            />
          </section>
        </PageLayout>
        <UserFeedback />
      </main>
      {window.matchMedia('(max-width: 900px)').matches && (
        <MobileAppBar
          completedKyc={user_details?.kyc_status?.L2?.passed_level}
        />
      )}
    </>
  )
}

export default MyTontineDashboard

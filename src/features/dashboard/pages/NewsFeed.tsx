import { To } from 'react-router-dom'
import { API } from '../../../common/api/API'
import Layout from '../../../common/components/Layout'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useMessageListener } from '../../../common/hooks/useWindowPostMessage'
import { DASHBOARD_NAVIGATION, PUBLIC } from '../../../routes/Route'
import style from '../style/NewsFeed.module.scss'

/**
 * Renders an iframe of news articles from the tontine.com website
 */
const NewsFeed = () => {
  const { isMobileOrTablet } = useDeviceScreen()
  const navigate = useCustomNavigation()

  useMessageListener(({ eventData, eventId }) => {
    if (eventId === 'ARTICLE_CLICKED') {
      navigate(
        `${DASHBOARD_NAVIGATION.NEWS_ARTICLE}/${(eventData as unknown as string).replace('/news/', '')}` as To
      )
    }
  })

  const iFrame = (
    <iframe
      title="News Feed"
      className={style['news-feed']}
      src={`${API.newsFeed}/news/?hideNewsContent=true`}
    />
  )

  if (isMobileOrTablet) {
    return (
      <Layout navigateTo={PUBLIC.GO_TO_PREV_PAGE} containerHeight="xlh">
        {iFrame}
      </Layout>
    )
  }

  return iFrame
}

export default NewsFeed

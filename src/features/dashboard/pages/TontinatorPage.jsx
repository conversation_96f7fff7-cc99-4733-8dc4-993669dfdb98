import PropTypes from 'prop-types'
import { lazy, useState } from 'react'
import { useLocation } from 'react-router-dom'
import Layout from '../../../common/components/Layout'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { useTranslate } from '../../../common/hooks/useTranslate'
import {
  adjustAndConvertToAgeMonthString,
  dobToYearsAndMonthsOld,
  getSupportedTontinatorParams,
} from '../../../common/utils/UtilFunctions'
import { FUNDED_PROGRESS, PRIVATE } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useIncomeScheduler } from '../../banking/hooks/useIncomeScheduler'
import CalculatorButtons from '../components/CalculatorButtons'
import TontinatorDashboardV2 from '../components/TontinatorDashboardV2'
import style from '../style/TontinatorPage.module.scss'
import { calculateYearForRetirementAge } from '../utils/UtilFunctions'

const SliderPage = lazy(() => import('../../dashboard/pages/SliderPage'))

/**
 * Renders the tontine calculator graph with the slider page where the user can
 * input different parameters to get a forecast. If the user is unauthenticated,
 * a registration modal is rendered.
 */
const TontinatorPage = ({ onboardingForecastParams }) => {
  const [showSliderPage, setShowSliderPage] = useState(false)

  const navigate = useCustomNavigation()
  const navigateToMyTontine = () => navigate(FUNDED_PROGRESS.PENSION_PLAN)
  const openSliderPage = () => setShowSliderPage(true)
  const t = useTranslate()
  const { isAuthenticated, context } = useAccountService()
  const {
    supportedCountry: { tontinatorParams },
  } = useSupportedCountries({
    alpha3CountryCode: onboardingForecastParams?.countryOfResidence,
  })

  const {
    incomeForecastParamsNumber,
    sliderSteps,
    setSliderSteps,
    ageThreshold,
    setIncomeForecastParamsNumber,
  } = useUserIncomeForecastParams({
    isAuthenticated,
    context,
    onboardingForecastParams,
    maxSimRetirementAge: tontinatorParams?.maxRetirementAge?.age,
  })

  return (
    <Layout
      containerHeight={'lite-build'}
      containerMt="mt-20"
      navigateTo={PRIVATE.MYTT_DASHBOARD}
      pageTitle={
        showSliderPage
          ? t('SLIDER_PAGE.HEADER')
          : t('MYTT_DASHBOARD.CARD_EDS_TITLE')
      }
      className={style['tontinator-page']}
      // On Safari it is possible for both of the headers to overlap for some
      // reason so this makes sure only one header is present when the slider
      // page is open
      hideMobileHeader={showSliderPage}
    >
      {showSliderPage && (
        <SliderPage
          pageTitle={t('MYTT_DASHBOARD.CARD_EDS_TITLE')}
          retirementSliders
          contributionSliders
          formsParams={incomeForecastParamsNumber}
          setShowSliderPage={setShowSliderPage}
          setNewFormData={setIncomeForecastParamsNumber}
          sliderSteps={sliderSteps}
          setSliderSteps={setSliderSteps}
          //FIXME: This is legacy and it NEEDS TO BE always
          loadingAgeThreshold
          ageThreshold={ageThreshold}
        />
      )}
      <p className={style['tontinator-page__disclaimer']}>
        {t('FORECAST.PAGE.DISCLAIMER')}
      </p>
      <TontinatorDashboardV2
        incomeForecastParams={incomeForecastParamsNumber}
        isAuthenticated={isAuthenticated}
      />
      <CalculatorButtons
        className={style['tontinator-page__bottom-buttons']}
        firstButtonAction={navigateToMyTontine}
        firstButtonLabel={t('CALCULATOR_BUTTON_TO_DASHBOARD')}
        secondButtonAction={openSliderPage}
        secondButtonLabel={t('BUTTON_LABEL.CALCULATOR')}
        secondButtonTestID={UI_TEST_ID.openSliderPageButton}
        hideCtaButton
      />
    </Layout>
  )
}

TontinatorPage.propTypes = {
  onboardingForecastParams: PropTypes.any,
}

export default TontinatorPage

/**
 * Chooses which income forecast params the tontinator needs to forecast with
 */
const useUserIncomeForecastParams = ({
  context,
  onboardingForecastParams,
  maxSimRetirementAge,
}) => {
  //Get the forecast params that were retrieved in `<MagicLogin />` response
  //when the user redeemed their magic token
  const { state: signUpForecastParams } = useLocation()
  // Init state with location state
  const [incomeForecastParamsNumber, setIncomeForecastParamsNumber] = useState(
    initializeStateWithIncomeParams({
      signUpForecastParams,
      onboardingForecastParams,
      context,
      maxSimRetirementAge,
    })
  )

  const { sliderSteps, setSliderSteps, ageThreshold } = useIncomeScheduler(
    incomeForecastParamsNumber
  )

  //Returns data needed for components
  return {
    incomeForecastParamsNumber:
      //Onboarding forecast params are ONLY present in ANON STATE
      //they will ALWAYS be undefined in auth state!
      incomeForecastParamsNumber ?? onboardingForecastParams,
    sliderSteps,
    setSliderSteps,
    ageThreshold,
    setIncomeForecastParamsNumber,
  }
}

/**
 * Initializes the income forecast params state when the `<TontinatorPage />` is
 * mounted. Draft plan data is prioritized. Specified order is followed:
 *
 * **DEFAULT** -> No 1,2,3 just generates default params for the user using the
 *    `generateDefaultParams`, the values are the same default values as in the
 *    "HOW IT WORKS" flow
 */
const initializeStateWithIncomeParams = ({
  signUpForecastParams,
  onboardingForecastParams,
  context,
  maxSimRetirementAge,
}) => {
  //AUTHENTICATED These params only become available if the user has registered
  //with the tontinator onboarding flow.
  if (signUpForecastParams) {
    const { contributions, demographic_data_current_age: current_age } =
      signUpForecastParams

    const { monthly_amount, onetime_amount, payout_age } = contributions

    const {
      yearsOldOnRetirement,
      monthsOldOnRetirement,
      retirementYear,
      retirementMonth,
    } = calculateYearForRetirementAge(current_age, payout_age)

    return {
      contributionAge: current_age,
      retirementAge: payout_age,
      yearsOld: yearsOldOnRetirement,
      monthsOld: monthsOldOnRetirement,
      year: retirementYear,
      month: retirementMonth,
      monthlyContribution: monthly_amount,
      oneTimeContribution: onetime_amount,
      countryOfResidence: context?.user_details?.residency,
      writeDraftPlan: true,
      //FIXME: https://github.com/TontineTrust/mytontine-webapp/issues/4311
      strategy: 'FII',
      paramsMode: 'TTF',
    }
  }

  //UNAUTHENTICATED Params from "HOW IT WORKS" flow also known as tontinator
  //flow and onboarding flow
  if (onboardingForecastParams) {
    let {
      contributionAge: adjustedContributionAge,
      retirementAge: adjustedPayoutAge,
    } = adjustAndConvertToAgeMonthString(
      onboardingForecastParams?.retirementAge,
      onboardingForecastParams?.contributionAge
    )

    //After the overflow has been handled we can have a scenario where the
    //payout age can exceed the max for a specific country, so this make sure
    //that does not happen, at least for now. Scenario: USA retirement age max:
    //84-11 Today is December, the output from
    //`adjustAndConvertToAgeMonthString` is contribution age: 84-11 and
    //retirement age: 85-0. This goes above the max thresholds. So we send to
    //the tontinator retirement age: 84-11 and contribution age: 84-10
    if (adjustedContributionAge?.age === maxSimRetirementAge) {
      const minMonthContributionAgeOld = 10
      adjustedPayoutAge = maxSimRetirementAge
      adjustedContributionAge = {
        age: onboardingForecastParams?.contributionAge,
        month: minMonthContributionAgeOld,
      }
    }

    return {
      ...onboardingForecastParams,
      contributionAge: adjustedContributionAge,
      retirementAge: adjustedPayoutAge,
      countryOfResidence: onboardingForecastParams?.countryOfResidence,
    }
  }

  //AUTHENTICATED If no params from onboarding flow, onboarding flow
  //registration and no plan, use default params
  return generateDefaultForecastParams(context?.user_details)
}

/**
 * Generates default forecast params for a user that has signed up without going
 * through the tontinator flow
 */
const generateDefaultForecastParams = (user_details) => {
  const { yearsOld, monthsOld } = dobToYearsAndMonthsOld(
    user_details?.date_of_birth
  )

  const supportedCountries = getSupportedTontinatorParams(
    user_details?.residency
  )

  const {
    defaultRetirementAgeSlider,
    defaultOneTimeSliderValue,
    defaultMonthlySliderValue,
  } = supportedCountries

  //Age month overflow, age month max values [0,11]
  const monthsOldOverflow = 12

  //Pick max retirement age from years old or default
  const defaultRetirementAge = Math.max(
    yearsOld,
    defaultRetirementAgeSlider?.age
  )

  //User retirement age and contribution age is same, add +1 month to retirement
  //age
  const monthsOldRetirement =
    defaultRetirementAge === yearsOld ? monthsOld + 1 : 0

  //Handles overflow in case months old is 12
  const interMonthsOldOnRetirement =
    monthsOldRetirement > monthsOldOverflow ? 0 : monthsOldRetirement

  //Adds year + 1 in case of month overflow
  const inetrYearsOld =
    monthsOld > monthsOldOverflow
      ? defaultRetirementAgeSlider.age + 1
      : defaultRetirementAge

  const retirementAge = {
    age: inetrYearsOld,
    month: interMonthsOldOnRetirement,
  }

  const {
    retirementYear,
    retirementMonth,
    yearsOldOnRetirement,
    monthsOldOnRetirement,
  } = calculateYearForRetirementAge(
    { age: yearsOld, month: monthsOld },
    retirementAge
  )

  return {
    retirementAge,
    monthlyContribution: defaultMonthlySliderValue,
    oneTimeContribution: defaultOneTimeSliderValue,
    year: retirementYear,
    month: retirementMonth,
    yearsOld: yearsOldOnRetirement,
    monthsOld: monthsOldOnRetirement,
    countryOfResidence: user_details?.residency,
    writeDraftPlan: true,
    //FIXME: https://github.com/TontineTrust/mytontine-webapp/issues/4311
    strategy: 'FII',
    paramsMode: 'TTF',
    target: 'deposit',
    targetMonthlyIncome: null,
  }
}

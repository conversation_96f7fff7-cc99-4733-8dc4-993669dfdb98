import PropTypes from 'prop-types'
import Layout from '../../../common/components/Layout'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import BottomCtaLiteLayout from '../components/BottomCtaLiteLayout'
import ExtendedTontinatorInputs from '../components/ExtendedTontinatorInputs'
import PublicTontinatorInputs from '../components/PublicTontinatorInputs'
import sliderStyle from '../style/SliderPage.module.scss'

/**
 * Renders a layout for the Public Tontinator with sign in and sign up layout
 */
const PublicTontinatorInputLayout = ({
  isSliderPageOpen,
  incomeForecastParams,
  setIncomeForecastParams,
  comparison,
  blueForecastParams,
  setBlueForecastParams,
  yellowForecastParams,
  setYellowForecastParams,
  setIsCompareOpen,
  setIsOpenSignInModal,
  setRegisterForm,
  isCompareOpen,
  setOpenSliderPage,
}) => {
  const { isMobileOrTablet } = useDeviceScreen()

  const sharedProps = {
    isSliderPageOpen,
    incomeForecastParams,
    setIncomeForecastParams,
    comparison,
    blueForecastParams,
    setBlueForecastParams,
    yellowForecastParams,
    setYellowForecastParams,
    extendDefault: (
      <ExtendedTontinatorInputs
        incomeForecastParams={incomeForecastParams}
        setIncomeForecastParams={setIncomeForecastParams}
        showSex
        trackSex={[
          {
            trackId: 'tontinator_sex_male',
          },
          {
            trackId: 'tontinator_sex_female',
          },
        ]}
        trackInvStrategies={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    extendBlue: (
      <ExtendedTontinatorInputs
        incomeForecastParams={blueForecastParams}
        setIncomeForecastParams={setBlueForecastParams}
        trackInvStrategies={{
          trackId: 'plan1_investment_strategy',
        }}
      />
    ),
    extendYellow: (
      <ExtendedTontinatorInputs
        incomeForecastParams={yellowForecastParams}
        setIncomeForecastParams={setYellowForecastParams}
        trackInvStrategies={{
          trackId: 'plan2_investment_strategy',
        }}
      />
    ),
  }

  if (isMobileOrTablet) {
    if (isSliderPageOpen) {
      return (
        <div className={sliderStyle['sliderPage']}>
          <Layout
            hideDividerHeader
            hideMobileHeader
            containerHeight="lite-build"
          >
            <PublicTontinatorInputs {...sharedProps} />
            <BottomCtaLiteLayout
              isSliderPageOpen={isSliderPageOpen}
              blueForecastParams={blueForecastParams}
              yellowForecastParams={yellowForecastParams}
              onClickBack={() => setIsCompareOpen(false)}
              setIsOpenSignInModal={setIsOpenSignInModal}
              onClickSignUpButton={() => setRegisterForm(true)}
              onClickMobileTopButtonOnly={() =>
                setOpenSliderPage((prev) => !prev)
              }
              onClickComparison={() => setIsCompareOpen(true)}
              isCompareOpen={isCompareOpen}
              onClickPlan1={() => {
                setIncomeForecastParams(blueForecastParams)
                setOpenSliderPage(false)
                setIsCompareOpen(false)
              }}
              onClickPlan2={() => {
                setIncomeForecastParams(yellowForecastParams)
                setOpenSliderPage(false)
                setIsCompareOpen(false)
              }}
              hideClickableText
            />
          </Layout>
        </div>
      )
    }
  } else {
    // No mobile detected, just return the normal desktop layout
    return <PublicTontinatorInputs {...sharedProps} />
  }
}

PublicTontinatorInputLayout.propTypes = {
  isSliderPageOpen: PropTypes.bool,
  setOpenSliderPage: PropTypes.func,
  incomeForecastParams: PropTypes.object,
  setIncomeForecastParams: PropTypes.func,
  comparison: PropTypes.bool,
  blueForecastParams: PropTypes.object,
  setBlueForecastParams: PropTypes.func,
  yellowForecastParams: PropTypes.object,
  setYellowForecastParams: PropTypes.func,
  setIsCompareOpen: PropTypes.func,
  setIsOpenSignInModal: PropTypes.func,
  setRegisterForm: PropTypes.func,
  isCompareOpen: PropTypes.bool,
}

export default PublicTontinatorInputLayout

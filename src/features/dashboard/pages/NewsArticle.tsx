import { useParams } from 'react-router-dom'
import { API } from '../../../common/api/API'
import Layout from '../../../common/components/Layout'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { PUBLIC } from '../../../routes/Route'
import style from '../style/NewsArticle.module.scss'

// in case the user navigates to the news article page without a slug
const fallbackNewsArticleSlug =
  '/oecd-tontines-offer-advantages-and-they-are-coming-soon/'

/**
 * Renders a single article from the tontine.com website
 */
const NewsArticle = () => {
  const { isMobileOrTablet } = useDeviceScreen()
  const { articleSlug } = useParams()

  const iFrame = (
    <iframe
      title="News Article"
      className={style['news-article']}
      src={`${API.newsFeed}/${articleSlug ?? fallbackNewsArticleSlug}?hideNewsContent=true`}
    />
  )

  if (isMobileOrTablet) {
    return (
      <Layout navigateTo={PUBLIC.GO_TO_PREV_PAGE}>
        <iframe
          title="News Article"
          className={style['news-article']}
          src={`${API.newsFeed}/${articleSlug ?? fallbackNewsArticleSlug}?hideNewsContent=true`}
        />
      </Layout>
    )
  }

  return iFrame
}

export default NewsArticle

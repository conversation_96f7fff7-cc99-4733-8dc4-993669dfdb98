import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { FUND_PENSION, PRIVATE } from '../../../routes/Route'
import { useBankingService } from '../../banking/hooks/useBankingService'
import NominalBalanceDashboard from '../components/NominalBalanceDashboard'
import style from '../style/NominalBalancePage.module.scss'

/**
 *  Renders the Current Nominal Balance page with the
 * `<NominalBalanceDashboard/>` component where the nominal balance graph is
 * displayed
 */
const NominalBalancePage = () => {
  const { bankContext, currentBankState, states } = useBankingService()
  //Hooks
  const t = useTranslate()

  const navigate = useCustomNavigation()
  const navigateToContributionHistory = () =>
    navigate(FUND_PENSION.CONTRIBUTION_HISTORY)

  return (
    <>
      <Layout
        pageTitle={t('NOMINAL_BALANCE_LABEL')}
        navigateTo={PRIVATE.MYTT_DASHBOARD}
        className={style['nominalBalancePage']}
        bottomSection={
          <NavigationButtons
            hideBackButton
            onClickSecond={() => navigateToContributionHistory()}
            secondButtonLabel={t('BANKING.PAGE_TITLE_CONTRIBUTION_HISTORY')}
            firstButtonTestID={UI_TEST_ID?.seeMyContributionHistory}
          />
        }
      >
        <ErrorBoundaryAndSuspense>
          <NominalBalanceDashboard
            bankContext={bankContext}
            states={states}
            currentBankState={currentBankState}
          />
        </ErrorBoundaryAndSuspense>
      </Layout>
    </>
  )
}

export default NominalBalancePage

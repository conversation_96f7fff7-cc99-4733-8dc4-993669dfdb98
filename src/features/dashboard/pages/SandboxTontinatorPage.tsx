import Layout from '../../../common/components/Layout'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useSupportedCountries } from '../../../common/hooks/useSupportedCountries'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PUBLIC } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import SandboxDashboard from '../components/SandboxDashboard'
import {
  chooseDefaultParams,
  useForecastParamsState,
} from '../hooks/usePreRegisterForecast'

const SandboxDashboardPage = () => {
  const {
    isAuthenticated,
    context: { user_details },
  } = useAccountService()
  const t = useTranslate()
  const { detectedCountry } = useLocalization()
  const { supportedCountry } = useSupportedCountries({
    alpha3CountryCode: user_details?.residency ?? detectedCountry?.alpha3,
  })
  const { tontinatorParams } = supportedCountry

  const defaultParams = chooseDefaultParams({
    tontinatorParams,
    supportedCountry,
    isAuthenticated,
    userDetails: user_details,
  })

  console.log(tontinatorParams)

  const {
    blueForecastParams,
    yellowForecastParams,
    incomeForecastParams,
    setBlueForecastParams,
    setYellowForecastParams,
    setIncomeForecastParams,
  } = useForecastParamsState({ defaultParams, tontinatorParams })

  return (
    <Layout
      pageTitle={t('MYTT_DASHBOARD.CARD_EDS_TITLE')}
      containerMt="mt-20"
      containerHeight="xlh"
      navigateTo={PUBLIC.GO_TO_PREV_PAGE}
    >
      <SandboxDashboard
        blueForecastParams={blueForecastParams}
        yellowForecastParams={yellowForecastParams}
        incomeForecastParams={incomeForecastParams}
        setBlueForecastParams={setBlueForecastParams}
        setYellowForecastParams={setYellowForecastParams}
        setIncomeForecastParams={setIncomeForecastParams}
      />
    </Layout>
  )
}

export default SandboxDashboardPage

import PropTypes from 'prop-types'
import { Trans } from 'react-i18next'
import Divider from '../../../common/components/Divider'
import Layout from '../../../common/components/Layout'
import LottieAnimation from '../../../common/components/LottieAnimation'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { ANIMATION } from '../../../common/constants/Animations'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import DateOfBirthModal from '../../authentication/components/DateOfBirthModal'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useDoBModal } from '../../authentication/hooks/useDoBModal'
import InfoBannerUnverifiedDoB from '../components/InfoBannerUnverifiedDoB'
import TontinatorInputs from '../components/TontinatorInputsV2'
import style from '../style/SliderPage.module.scss'

/**
 * Renders a page with input forms and sliders for gathering
 * information about retirement and contribution amounts.
 */
const SliderPage = ({
  formsParams,
  setShowSliderPage,
  setNewFormData,
  pageTitle,
  className,
  loadingAgeThreshold,
}) => {
  const t = useTranslate()
  const closeSliderPage = () => setShowSliderPage(false)

  const {
    context: { user_details },
  } = useAccountService()

  const { openDobModal, setOpenDobModal } = useDoBModal()

  return (
    <main className={style['sliderPage']}>
      <DateOfBirthModal
        isOpen={openDobModal}
        setOpenDobModal={setOpenDobModal}
        isLoadingPlanAndThresholds={loadingAgeThreshold}
      />
      <Layout
        containerMt="nomt"
        containerHeight="xlh"
        onClickAction={closeSliderPage}
        pageTitle={pageTitle}
        className={className}
      >
        <InfoBannerUnverifiedDoB
          trans={
            <Trans
              i18nKey={'INFO_BANNER.NO_UNVERIFIED_DOB'}
              values={{
                yearsOld: user_details?.unverified_age?.age,
                monthsOld: user_details?.unverified_age?.month,
              }}
            />
          }
          onClickClickableText={() => {
            setOpenDobModal(true)
          }}
        />
        <section className={style['sliderPage__sliders']}>
          {!loadingAgeThreshold ? (
            <LottieAnimation
              autoplay
              loop
              animationName={ANIMATION.loadingLightBlueDots}
              className={style['sliderPage-animation']}
            />
          ) : (
            <TontinatorInputs
              formData={formsParams}
              setFormData={setNewFormData}
              showRetirementScheduler
              hideCurrentAgeSlider
              hideRetirementSliders
              contributionSlidersClassName={
                style['sliderPage__contribution-sliders']
              }
            />
          )}
        </section>

        <Divider className={style[`sliderPage__divider`]} />

        <NavigationButtons
          hideBackButton
          disabledFixedOnMobile
          className={style[`sliderPage__navigation-btns`]}
          onClickSecond={closeSliderPage}
          secondButtonLabel={t('SLIDER_PAGE.BUTTON_LABEL')}
          dataTestIDSecondBtn={UI_TEST_ID.closeSliderPageButton}
        />
      </Layout>
    </main>
  )
}

SliderPage.propTypes = {
  setNewFormData: PropTypes.func,
  setShowSliderPage: PropTypes.func,
  onlySliders: PropTypes.bool,
  formsParams: PropTypes.object,
  retirementSliders: PropTypes.bool,
  contributionSliders: PropTypes.bool,
  pageTitle: PropTypes.string,
  contributionSlidersClassName: PropTypes.string,
  retirementAgeSlidersClassName: PropTypes.string,
  className: PropTypes.string,
  sliderSteps: PropTypes.array,
  setSliderSteps: PropTypes.func,
  loadingAgeThreshold: PropTypes.bool,
  ageThreshold: PropTypes.object,
  inputsPrefix: PropTypes.string,
}

export default SliderPage

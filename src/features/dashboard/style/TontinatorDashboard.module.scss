@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

/** @define tontinatorDashboard */
.tontinatorDashboard {
  height: 600px;
  overflow: scroll;
  padding: 25px 25px 2px 2px;

  // flex-grow: 1;
  width:
    68%;

  &__divider {
    margin-top: 0;
    margin-bottom: 1.25rem;

    &--hidden {
      margin-top: 20px;
      visibility: hidden;
    }
  }

  &__banner-container {
    margin: 15px 0 10px 0;
  }

  &__section {
    border-radius: 5px;
    background-color: colors.$white;
    padding: 10px;
    box-shadow: variables.$chart-box-shadow;
  }

  &__error-text {
    text-align: center;
  }

  &__graph-title {
    display: block;
    font-size: variables.$font-size-s !important;
  }

  &__payout-container {
    @include mixins.flex-layout;
    margin-bottom: 1.25rem;
    margin-top: 1.875rem;
  }

  &__payout-center {
    max-width: 700px;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    padding: 25px 2px 2px 2px;
    width: 100%;

    &__divider {
      display: none;
    }

    &__payout-center {
      max-width: 100% !important;
    }
  }
}
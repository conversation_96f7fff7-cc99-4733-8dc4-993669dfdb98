@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

/** @define sliderPage */
.sliderPage {
  top: 0;
  left: 0;
  position: absolute;
  width: 100% !important;
  background-color: colors.$white;
  z-index: 99999;
  height: 90svh;
  overflow-y: hidden;

  &__retirement-age-sliders,
  &__contribution-sliders {
    @include mixins.flex-layout(column);
    margin-top: 1.25rem;
    gap: 1.25rem;
    max-width: 400px;
  }

  &__sliders {
    @include mixins.flex-layout(column);
    margin-top: 20px;
  }

  &__divider {
    margin-top: 70px;
  }

  &__nav-btns-container {
    @include mixins.flex-layout;
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    z-index: 9999;
    height: 100svh;

    &__divider {
      display: none;
    }

    &__retirement-age-sliders,
    &__contribution-sliders {
      width: 100%;
      margin-bottom: 1.25rem;
      gap: 1.25rem;
      flex-wrap: wrap;
    }
  }
}
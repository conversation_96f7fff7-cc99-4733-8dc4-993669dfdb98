/* stylelint-disable */
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/variables';

/** @define sandbox-dashboard */
.sandbox-dashboard {

    &__dashboards {
        @include mixins.flex-layout($align-items: flex-start, $justify-content: space-between);
    }

    &__bottom-layout {
        padding-block: 2rem;
        border-top: 2px solid colors.$gray-mist;
        @include mixins.flex-layout($gap: 20px);
    }

    &__compare-button {
        max-width: 30%;
        min-width: 25%;
    }

    &__investment-strategy {
        height: 4.125rem
    }

    @media only screen and (max-width: variables.$mobile-devices) {

        &__bottom-layout {
            max-width: unset;
            margin-top: 2.5rem;
            padding: 2rem 1rem;
            @include mixins.flex-layout(column, $align-items: center, $justify-content: space-between, $gap: 0.5rem);
        }

        &__compare-button {
            max-width: unset;
            min-width: unset;
        }
    }
}
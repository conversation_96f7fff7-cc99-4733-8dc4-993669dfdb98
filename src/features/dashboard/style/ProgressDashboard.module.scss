@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define progress-dashboard */
.progress-dashboard {
  @include mixins.flex-layout(column);
  width: 100%;
  border-radius: variables.$rounded;
  background-color: colors.$white;
  margin-bottom: variables.$mytt-dashboard-element-spacing;
  margin-top: 2rem;

  &__chart {
    margin-top: 20px;
  }

  &__animation-container {
    @include mixins.flex-layout;
    margin: 3.75rem 0;
    width: 100%;
  }

  &__animation {
    width: 100%;
    height: 100%;

    @media only screen and (max-width: variables.$mobile-devices) {
      height: 200px;
      width: 400px;
    }
  }

  &__divider {
    margin-top: 3.75rem;
  }

  &__graph-legend {
    margin-top: 1.875rem;
  }

  &__loading-animation {
    width: 100px;
    height: 4.25rem;
    transform: scale(3);
  }

  &__next-payout {
    width: 100%;
    margin-block: 0.625rem;
    background-color: colors.$gray-faint;
    padding: 10px;
  }

  &__payout-container {
    width: 100%;
    @include mixins.flex-layout;
    @include mixins.payout-info($width: 80%);
  }

  &__button-container {
    @include mixins.flex-layout;
  }

  &__button {
    max-width: variables.$button-max-width;
  }

  //Mobile devices scss starts from here
  @media only screen and (max-width: variables.$mobile-devices) {
    padding: 0.625rem;
    margin-top: 20px;

    &__next-payout {
      display: none !important;
    }

    &__button-container {
      margin: variables.$mytt-dashboard-element-spacing 0;
    }

    &__payout-container {
      @include mixins.payout-info($width: 100%);
    }

    &__divider {
      margin-bottom: 1rem;
    }

    &__graph-legend {
      @include mixins.flex-layout(
        row !important,
        $justify-content: space-between !important
      );
      width: 100% !important;
      margin-top: 0;
    }
  }
}

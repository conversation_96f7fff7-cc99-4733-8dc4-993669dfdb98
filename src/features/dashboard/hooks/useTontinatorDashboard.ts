import { useState } from 'react'
import { track } from '../../../common/analytics/Analytics'
import { EventToTrack } from '../../../common/analytics/Analytics.types'
import { EVENT_DESC } from '../../../common/analytics/EventDescription'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import { CALCULATOR_FORMS } from '../utils/consts'

/**
 * Puts the Tontinator in a loading state if the UI is initializing or if a
 * forecast is being performed. If user isAuthenticated then  we need to wait
 * for the UI to initialize with age thresholds, if not then we just need to
 * wait for the forecast to complete
 *
 */
const isLoadingUI = (
  renderUI: boolean,
  isLoading: boolean,
  isAuthenticated: boolean
) => {
  if (isAuthenticated) {
    return !renderUI || isLoading
  }

  return isLoading
}

const shouldHideAnnuity = (
  incomeForecastParams: IncomeForecastParams,
  hide: boolean
) => incomeForecastParams?.countryOfResidence === 'JPN' || hide

const messageFromToggle = (toggles: {
  percentage: boolean
  breakeven: boolean
  inflation: boolean
}) => {
  if (toggles?.percentage) {
    return 'BANNER_INFO.PERCENT'
  }
  if (toggles?.breakeven) {
    return 'BANNER_INFO.BREAKEVEN'
  }
  if (toggles?.inflation) {
    return 'BANNER_INFO.INFLATION'
  }

  return ''
}

/**
 * Controls the graph switches, whether to hide or show certain graph elements
 *
 * - **Breakeven** and **percentage** can be `true` at the same time
 * - **Breakeven** or **percentage** set **inflation** to `false`
 * - **Inflation** sets **breakeven** and **percentage** to `false`
 */
const useGraphToggles = () => {
  //TODO: useReducer is the way to go here, if a state depends on another state
  const [percentage, setPercentage] = useState(
    CALCULATOR_FORMS.GRAPH_TOGGLES_DEFAULT_VALUES.percentage
  )
  const [breakeven, setBreakeven] = useState(
    CALCULATOR_FORMS.GRAPH_TOGGLES_DEFAULT_VALUES.breakeven
  )
  const [inflation, setInflation] = useState(
    CALCULATOR_FORMS.GRAPH_TOGGLES_DEFAULT_VALUES.inflation
  )
  const [bannerMessageKey, setBannerMessageKey] = useState(
    messageFromToggle({
      breakeven,
      inflation,
      percentage,
    })
  )

  /**
   * Enables percentage view of the chart and renders percentage banner also
   * disables inflation toggle and inflation banner.
   */
  const handlePercentage = () => {
    setPercentage((prevState) => {
      setInflation(false)

      setBannerMessageKey(
        messageFromToggle({
          percentage: !prevState,
          // Makes sure if both breakeven and percentage are toggled
          // to only display the most recently clicked toggle banner
          breakeven: breakeven && prevState,
          inflation: false,
        })
      )

      return !prevState
    })
  }

  /**
   * Enables breakeven circles on the chart and enables breakeven banner, also
   * disables inflation and inflation banner
   */
  const handleBreakeven = () =>
    setBreakeven((prevState) => {
      setInflation(false)

      setBannerMessageKey(
        messageFromToggle({
          // Makes sure if both breakeven and percentage are toggled
          // to only display the most recently clicked toggle banner
          percentage: percentage && prevState,
          breakeven: !prevState,
          inflation: false,
        })
      )

      return !prevState
    })

  /**
   * Enables inflation view of the chart and inflation banner. Disables
   * percentage and breakeven
   */
  const handleInflation = () =>
    setInflation((prevState) => {
      setBreakeven(false)
      setPercentage(false)

      setBannerMessageKey(
        messageFromToggle({
          percentage: false,
          breakeven: false,
          inflation: !prevState,
        })
      )

      return !prevState
    })

  return {
    bannerMessageKey,
    percentage,
    handlePercentage,
    breakeven,
    handleBreakeven,
    inflation,
    handleInflation,
  }
}

const trackChartHover = ({ event }: { event: EventToTrack }) => {
  void track({
    event,
    properties: {
      object_id: 'tontinator',
      description: EVENT_DESC.chartHoverOrTap,
    },
  })
}

export { isLoadingUI, shouldHideAnnuity, trackChartHover, useGraphToggles }

import { useState } from 'react'
import Button from '../../../common/components/Button'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { SandboxDashboardProps } from '../types/SandboxDashboard.types'
import ComparePlanButtons from './ComparePlanButtons'
import InvStrategiesDropdown from './InvStrategiesDropdown'
import PensionPlanDashboard from './PensionPlanDashboard'
import PublicTontinatorInputs from './PublicTontinatorInputs'
import PublicTontinatorInputsModal from './PublicTontinatorInputsModal'
import TontinatorDashboard from './TontinatorDashboardV2'

import { useTranslate } from '../../../common/hooks/useTranslate'
import sandboxStyle from '../style/SandboxDashboard.module.scss'
import tontinatorInputsModalStyle from '../style/TontinatorInputsModal.module.scss'

const SandboxDashboard = ({
  incomeForecastParams,
  setIncomeForecastParams,
  blueForecastParams,
  setBlueForecastParams,
  yellowForecastParams,
  setYellowForecastParams,
}: SandboxDashboardProps) => {
  const t = useTranslate()
  const [isCompareOpen, setIsCompareOpen] = useState(false)
  const [isParamsOpen, setIsParamsOpen] = useState(false)

  const { isMobileOrTablet } = useDeviceScreen()

  const inputsParams = {
    blueForecastParams: blueForecastParams,
    comparison: isCompareOpen,
    incomeForecastParams: incomeForecastParams,
    setIncomeForecastParams: setIncomeForecastParams,
    setBlueForecastParams: setBlueForecastParams,
    setYellowForecastParams: setYellowForecastParams,
    yellowForecastParams: yellowForecastParams,
    extendDefault: (
      <InvStrategiesDropdown
        value={incomeForecastParams?.strategy}
        onChange={(strategy) => {
          setIncomeForecastParams({
            ...incomeForecastParams,
            strategy,
          })
        }}
        label={'Investment Strategy'}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
        className={sandboxStyle['sandbox-dashboard__investment-strategy']}
      />
    ),
    extendBlue: (
      <InvStrategiesDropdown
        value={blueForecastParams?.strategy}
        onChange={(strategy) => {
          setBlueForecastParams({
            ...blueForecastParams,
            strategy,
          })
        }}
        label={'Investment Strategy'}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    extendYellow: (
      <InvStrategiesDropdown
        value={yellowForecastParams?.strategy}
        onChange={(strategy) => {
          setYellowForecastParams({
            ...yellowForecastParams,
            strategy,
          })
        }}
        label={'Investment Strategy'}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    propsForDefaultLayout: {
      hideRetirementSliders: true,
      showRetirementScheduler: true,
    },
    propsForBlueLayout: {
      hideRetirementSliders: true,
      showRetirementScheduler: true,
    },
    propsForYellowLayout: {
      hideRetirementSliders: true,
      showRetirementScheduler: true,
    },
  }

  return (
    <main>
      <section className={sandboxStyle['sandbox-dashboard__dashboards']}>
        {isCompareOpen ? (
          <PensionPlanDashboard
            dataToDraw={[blueForecastParams, yellowForecastParams]}
          />
        ) : (
          <TontinatorDashboard incomeForecastParams={incomeForecastParams} />
        )}
        {!isMobileOrTablet && <PublicTontinatorInputs {...inputsParams} />}
        {isMobileOrTablet && isParamsOpen && (
          <PublicTontinatorInputsModal
            isOpen={isParamsOpen}
            tontinatorProps={inputsParams}
            className={tontinatorInputsModalStyle['tontinator-inputs-modal']}
          >
            <div
              className={
                tontinatorInputsModalStyle[
                  'tontinator-inputs-modal__buttons-wrapper'
                ]
              }
            >
              <Button
                onClick={() => setIsParamsOpen(false)}
                className={
                  tontinatorInputsModalStyle[
                    'tontinator-inputs-modal__compare-button'
                  ]
                }
              >
                {t('CHECK_OTHER_SCENARIOS')}
              </Button>
              {isCompareOpen ? (
                <ComparePlanButtons
                  blueForecastParams={blueForecastParams}
                  hideAlternativeButton
                  yellowForecastParams={yellowForecastParams}
                  onClickPlan1={() => {
                    setIsCompareOpen(false)
                    setIncomeForecastParams(blueForecastParams)
                    setIsParamsOpen(false)
                  }}
                  onClickPlan2={() => {
                    setIsCompareOpen(false)
                    setIncomeForecastParams(yellowForecastParams)
                    setIsParamsOpen(false)
                  }}
                />
              ) : (
                <Button
                  className={
                    tontinatorInputsModalStyle[
                      'tontinator-inputs-modal__compare-button'
                    ]
                  }
                  onClick={() => setIsCompareOpen((prev) => !prev)}
                >
                  {t('BUTTON_TO_COMPARE')}
                </Button>
              )}
            </div>
          </PublicTontinatorInputsModal>
        )}
      </section>
      <section className={sandboxStyle['sandbox-dashboard__bottom-layout']}>
        {isMobileOrTablet && (
          <Button
            onClick={() => setIsParamsOpen(true)}
            className={sandboxStyle['sandbox-dashboard__compare-button']}
          >
            {t('CHECK_OTHER_SCENARIOS')}
          </Button>
        )}
        {isCompareOpen ? (
          <ComparePlanButtons
            blueForecastParams={blueForecastParams}
            hideAlternativeButton
            yellowForecastParams={yellowForecastParams}
            onClickPlan1={() => {
              setIsCompareOpen(false)
              setIncomeForecastParams(blueForecastParams)
            }}
            onClickPlan2={() => {
              setIsCompareOpen(false)
              setIncomeForecastParams(yellowForecastParams)
            }}
          />
        ) : (
          <Button
            className={sandboxStyle['sandbox-dashboard__compare-button']}
            onClick={() => setIsCompareOpen((prev) => !prev)}
          >
            {t('BUTTON_TO_COMPARE')}
          </Button>
        )}
      </section>
    </main>
  )
}

export default SandboxDashboard

import { useState } from 'react'
import MobileIncomeStatTip from '../../../common/components/MobileIncomeStatTip'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { CompareIncomeStatsProps } from '../types/Dashboard.types'
import IncomeStatsExtended from './IncomeStatsExtended'

/**
 * Renders two IncomeStatsExtended components, one for each plan.
 */
const CompareIncomeStats = ({
  plan1,
  plan2,
  currency,
}: CompareIncomeStatsProps) => {
  const t = useTranslate()

  const [plan1Active, setPlan1Active] = useState(true)
  const [plan2Active, setPlan2Active] = useState(
    // Disable when init in mobile view
    !window?.matchMedia('(max-width: 900px)')?.matches
  )

  const { isMobileOrTablet } = useDeviceScreen((isMobileOrTablet) => {
    if (isMobileOrTablet) {
      setPlan1Active(true)
      setPlan2Active(false)
    } else {
      setPlan2Active(true)
    }
  })

  const sharedProps = {
    incomeLabel: t('FORECAST_PAGE.PAYOUTS_BY_100_LABEL'),
    currency: currency as 'USD' | 'EUR',
  }

  return (
    <>
      {isMobileOrTablet && plan1 && plan2 && (
        <MobileIncomeStatTip
          tooltipText1={t('PLAN1_INPUT_GROUP')}
          tooltipText2={t('PLAN2_INPUT_GROUP')}
          onClickTip1={() => {
            setPlan2Active(false)
            setPlan1Active(true)
          }}
          onClickTip2={() => {
            setPlan1Active(false)
            setPlan2Active(true)
          }}
          tip1Active={Boolean(plan1Active && plan1)}
          tip2Active={Boolean(plan2Active && plan2)}
        />
      )}

      {plan1Active && plan1 && (
        <IncomeStatsExtended
          variant="blue-faint"
          {...sharedProps}
          {...plan1}
          contributionLabel={t('INCOME_STAT_CONTRIBUTION_LABEL', {
            incomeStartAge: plan1?.incomeStartAge,
          })}
        />
      )}
      {plan2Active && plan2 && (
        <IncomeStatsExtended
          variant="yellow-faint"
          {...sharedProps}
          {...plan2}
          contributionLabel={t('INCOME_STAT_CONTRIBUTION_LABEL', {
            incomeStartAge: plan2?.incomeStartAge,
          })}
        />
      )}
    </>
  )
}

export default CompareIncomeStats

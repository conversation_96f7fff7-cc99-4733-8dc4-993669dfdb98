import BannerMessage from '../../../common/components/BannerMessage'
import ClickableText from '../../../common/components/ClickableText'
import InfoBannerUserAge from '../../../common/components/InfoBannerUserAge'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { InfoBannerUnverifiedDoBProps } from '../types/Dashboard.types'

/**
 * Checks if the user has a verified or unverified date of birth attached to
 * their account. If there is a full date of birth attached then
 * `<InfoBannerUserAge />` is rendered. Otherwise a `<ClickableText />` is
 * rendered to prompt the user to start the DoB update process
 */
const InfoBannerUnverifiedDoB = ({
  onClickClickableText,
  infoBannerText,
  infoBannerClickableText,
  trans,
}: InfoBannerUnverifiedDoBProps) => {
  const {
    context: { user_details },
  } = useAccountService()

  if (
    user_details?.verified_date_of_birth ||
    user_details?.unverified_date_of_birth_set_by_user
  ) {
    return (
      <BannerMessage>
        <InfoBannerUserAge user_details={user_details} />
      </BannerMessage>
    )
  }

  if (trans) {
    return (
      <BannerMessage>
        <span onClick={onClickClickableText}>{trans}</span>
      </BannerMessage>
    )
  }

  return (
    <ClickableText
      text={infoBannerText ?? ''}
      clickableText={infoBannerClickableText}
      onClick={onClickClickableText}
    />
  )
}

export default InfoBannerUnverifiedDoB

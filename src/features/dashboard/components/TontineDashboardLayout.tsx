import { useLocation } from 'react-router-dom'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { isLite } from '../../../config/lite'
import { MYTT_DASHBOARD } from '../../../routes/Route'
import style from '../style/TontineDashboardLayout.module.scss'
import { TontineDashboardLayoutProps } from '../types/TontineDashboardLayout.types'
import DashboardNavigation from './DashboardNavigation'

/**
 * Child component to be rendered between the
 * `<DashboardNavigation />` and `<NewsFeed />` component
 *
 * Layout used for the Tontine Dashboard, the child component is
 * rendered in the middle middle, sandwiched by the DashboardNavigation and the
 * NewsFeed.
 */
const TontineDashboardLayout = ({
  children,
  noPaddingMain,
}: TontineDashboardLayoutProps) => {
  const { pathname } = useLocation()
  const { isMobileOrTablet } = useDeviceScreen()
  const shouldDisablePadding =
    pathname.includes(MYTT_DASHBOARD.NEWS_FEED) && !isMobileOrTablet

  return (
    <main className={style['tontine-dashboard-layout']}>
      <section className={style[`tontine-dashboard-layout__container`]}>
        {!isLite && (
          <DashboardNavigation
            className={style[`tontine-dashboard-layout__nav`]}
          />
        )}

        <section
          className={
            style[
              `tontine-dashboard-layout__main-content${noPaddingMain || shouldDisablePadding ? '--no-padding' : ''}`
            ]
          }
        >
          <ErrorBoundaryAndSuspense hideNavButton>
            {children}
          </ErrorBoundaryAndSuspense>
        </section>
      </section>
    </main>
  )
}

export default TontineDashboardLayout

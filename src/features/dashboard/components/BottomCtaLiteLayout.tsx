import { toast } from 'react-toastify'
import { track } from '../../../common/analytics/Analytics'
import { PensionPlan } from '../../../common/analytics/EventData'
import { EVENT_DESC } from '../../../common/analytics/EventDescription'
import Button from '../../../common/components/Button'
import ButtonAndClickableText from '../../../common/components/ButtonAndClickableText'
import Icon from '../../../common/components/Icon'
import ToastMessage from '../../../common/components/ToastMessage'
import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { debounce, deepEqual } from '../../../common/utils/UtilFunctions'
import { isLite } from '../../../config/lite'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useLiteAuth } from '../../authentication/hooks/useLiteAuth'
import style from '../style/BottomCtaLiteLayout.module.scss'
import { BottomCtaLiteLayoutProps } from '../types/Dashboard.types'
import ComparePlanButtons from './ComparePlanButtons'

/**
 * Sign in for mobile or desktop
 */
const BottomCtaLiteLayout = ({
  hideAlternativeButton,
  isCompareOpen,
  onClickMobileTopButtonOnly,
  onClickBack,
  onClickPlan1,
  onClickPlan2,
  onClickComparison,
  onClickSignUpButton,
  setIsOpenSignInModal,
  blueForecastParams,
  yellowForecastParams,
  isSliderPageOpen,
  hideClickableText,
  incomeForecastParams,
}: BottomCtaLiteLayoutProps) => {
  const { isMobileOrTablet } = useDeviceScreen()
  const { isAuth, error } = useLiteAuth()
  const { send, currentState, context } = useAccountService()
  const t = useTranslate()

  const submitNewPlan = debounce(() => {
    send({
      type: 'UPDATE_MTL_DRAFT_PLAN',
      payload: {
        draftPensionPlan: incomeForecastParams,
        successCallback: () => {
          toast.success(
            <ToastMessage
              title={t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT')}
            />
          )
          void track({
            event: PensionPlan.lite_plan_updated,
            properties: {
              object_value: incomeForecastParams,
              object_id: 'tontinator',
            },
          })
        },
        failureCallback: () =>
          toast.error(<ToastMessage title={t('ERROR_GENERIC')} />),
      },
    })
  }, 1_000)

  return (
    <section className={style[`bottom-cta-lite-layout`]}>
      {!isCompareOpen && !error && (
        <>
          <Icon
            fileName={ASSET.tontiRaisedHands}
            className={
              style[`bottom-cta-lite-layout__mascot${isAuth ? '--auth' : ''}`]
            }
          />
          <Icon
            fileName={ASSET.tontiHappy}
            className={
              style[`bottom-cta-lite-layout__mascot-1${isAuth ? '--auth' : ''}`]
            }
          />
        </>
      )}

      {!isCompareOpen && isMobileOrTablet && (
        <div className={style[`bottom-cta-lite-layout__buttons`]} />
      )}

      {!isCompareOpen && (
        <>
          {isMobileOrTablet && !hideAlternativeButton && (
            <Button
              variant="alternative"
              onClick={onClickMobileTopButtonOnly}
              dataTestID={UI_TEST_ID.openSliderPageButton}
              trackActivity={{
                trackId: 'tontinator_slider_page',
                eventDescription: EVENT_DESC.tontinatorOpenSliderPage,
              }}
            >
              {isSliderPageOpen
                ? t('CHECK_UPDATED_CHART')
                : t('CHECK_OTHER_SCENARIOS')}
            </Button>
          )}
          <Button
            variant="blue"
            onClick={onClickComparison}
            dataTestID={UI_TEST_ID.openComparePlanButton}
            trackActivity={{
              trackId: 'tontinator_open_compare_plan',
              eventDescription: EVENT_DESC.comparePlanBtn,
            }}
          >
            {t('BUTTON_TO_COMPARE')}
          </Button>

          {isAuth && (
            <Button
              loading={currentState === 'UPDATING_MTL_PENSION_PLAN'}
              onClick={submitNewPlan}
              trackActivity={{
                trackId: 'tontinator_update_lite_plan',
              }}
              disabled={
                // Already has this plan
                // no need to spam requests
                deepEqual(
                  incomeForecastParams,
                  context.liteData?.pensionPlan as object
                )
              }
              dataTestID={UI_TEST_ID.updateLitePlanBtn}
            >
              {t('TONTINATOR_UPDATE_PLAN')}
            </Button>
          )}

          {!isAuth && isLite && (
            <ButtonAndClickableText
              buttonVariant="primary--animated"
              buttonLabel={t('BUTTON_TO_PRE_REG')}
              buttonOnClick={onClickSignUpButton}
              textLabel={hideClickableText ? '' : t('REFERRAL_BANNER_SIGN_IN')}
              textOnClick={() => setIsOpenSignInModal?.(true)}
              buttonDataTestID={UI_TEST_ID.registerButton}
              textDataTestID={UI_TEST_ID.loginBtnDesktop}
              buttonTrackActivity={{
                trackId: 'tontinator_open_register_screen',
                eventDescription: EVENT_DESC.tontinatorOpenRegisterScreen,
              }}
              textTrackActivity={{
                trackId: 'tontinator_login_text',
                eventDescription: EVENT_DESC.tontinatorLoginText,
              }}
            />
          )}
        </>
      )}

      {isCompareOpen && (
        <>
          {!isMobileOrTablet && (
            <Button
              variant="back--light"
              onClick={onClickBack}
              icon={ASSET.icononboardinarrowback}
              className={style[`bottom-cta-lite-layout__buttons-back`]}
              dataTestID={UI_TEST_ID.backBtnDesktop}
              trackActivity={{
                trackId: 'compare_plan_back',
                eventDescription: EVENT_DESC.comparePlanBack,
              }}
            >
              {t('BACK_BUTTON_LABEL')}
            </Button>
          )}

          <ComparePlanButtons
            blueForecastParams={blueForecastParams}
            hideAlternativeButton={hideAlternativeButton}
            isMobileOrTablet={isMobileOrTablet}
            isSliderPageOpen={isSliderPageOpen}
            onClickMobileTopButtonOnly={onClickMobileTopButtonOnly}
            onClickPlan1={onClickPlan1}
            onClickPlan2={onClickPlan2}
            yellowForecastParams={yellowForecastParams}
          />
        </>
      )}
    </section>
  )
}

export default BottomCtaLiteLayout

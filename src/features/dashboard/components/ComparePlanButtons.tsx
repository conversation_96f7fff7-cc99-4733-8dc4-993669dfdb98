import { EVENT_DESC } from '../../../common/analytics/EventDescription'
import Button from '../../../common/components/Button'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ComparePlanButtonsProps } from '../types/Dashboard.types'

/**
 * Button layout for mobile and desktop for MyTontine lite
 */
const ComparePlanButtons = ({
  onClickMobileTopButtonOnly,
  onClickPlan1,
  onClickPlan2,
  blueForecastParams,
  yellowForecastParams,
  isSliderPageOpen,
  hideAlternativeButton,
  isMobileOrTablet,
}: ComparePlanButtonsProps) => {
  const t = useTranslate()

  return (
    <>
      {isMobileOrTablet && !hideAlternativeButton && (
        <Button
          variant="alternative"
          onClick={onClickMobileTopButtonOnly}
          dataTestID={UI_TEST_ID.openSliderPageButton}
          trackActivity={{
            trackId: 'compare_plan_slider_page',
            eventDescription: EVENT_DESC.tontinatorOpenSliderPage,
          }}
        >
          {isSliderPageOpen
            ? t('CHECK_UPDATED_CHART')
            : t('CHECK_OTHER_SCENARIOS')}
        </Button>
      )}

      <Button
        variant="blue"
        onClick={onClickPlan1}
        dataTestID={UI_TEST_ID.pickPlan1Button}
        trackActivity={{
          trackId: 'compare_plan_choose_plan1',
          eventDescription: EVENT_DESC.plan1Choose,
          value: blueForecastParams,
        }}
      >
        {t('BUTTON_KEEP_PLAN_1')}
      </Button>
      <Button
        variant="yellow"
        onClick={onClickPlan2}
        dataTestID={UI_TEST_ID.pickPlan2Button}
        trackActivity={{
          trackId: 'compare_plan_choose_plan2',
          eventDescription: EVENT_DESC.plan2Choose,
          value: yellowForecastParams,
        }}
      >
        {t('BUTTON_KEEP_PLAN_2')}
      </Button>
    </>
  )
}

export default ComparePlanButtons

import Button from '../../../common/components/Button'
import { CalculatorButtonsProps } from '../types/Dashboard.types'

/**
 * Renders two buttons with a container
 */
const CalculatorButtons = ({
  firstButtonAction,
  firstButtonLabel,
  secondButtonAction,
  secondButtonLabel,
  secondButtonTestID,
  firstButtonTestID,
  className,
  firstDisabled,
  secondDisabled,
  hideCtaButton,
}: CalculatorButtonsProps) => {
  return (
    <article className={`${className}`}>
      <Button
        disabled={secondDisabled}
        onClick={secondButtonAction}
        dataTestID={secondButtonTestID}
        variant="blue"
      >
        {secondButtonLabel}
      </Button>
      {!hideCtaButton && (
        <Button
          onClick={firstButtonAction}
          disabled={firstDisabled}
          dataTestID={firstButtonTestID}
        >
          {firstButtonLabel}
        </Button>
      )}
    </article>
  )
}

export default CalculatorButtons

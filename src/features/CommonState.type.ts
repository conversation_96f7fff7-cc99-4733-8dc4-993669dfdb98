type ErrorStorage = {
  translatedError?: string
  apiErrorMessage?: object
}

/**
 * Generic event payload data
 */
interface EventWithCommonPayload {
  abortController?: AbortController
  /**
   * Issued when a service has concluded with success, usually if you want to
   * render a toast message for example
   */
  successCallback?: (data?: unknown) => void
  /**
   * Issued when a service has encountered an exception in `catch` block
   */
  failureCallback?: (error?: ErrorStorage) => void
  /**
   * Always issued when a service has concluded
   */
  finallyCallback?: () => void
}

type AgeMonth = {
  age: number
  month: number
}

type YearMonth = {
  age: number
  month: number
}

export type { EventWithCommonPayload, ErrorStorage, AgeMonth, YearMonth }

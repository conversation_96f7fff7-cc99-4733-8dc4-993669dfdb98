- [FaceTec Web SDK](#facetec-web-sdk)
  - [Things to keep in mind](#things-to-keep-in-mind)
  - [How to update the FaceTec web SDK](#how-to-update-the-facetec-web-sdk)

# FaceTec Web SDK

- `FaceBiometrics.js` - exposes essential functions in order to use face scan
  anywhere with any JavaScript project. This is what you import in order to use
  face scan. Docs about what each functions does, is located in
  `FaceBiometrics.d.ts`, which also provides JSDOC when you hover on any
  function you use from `FaceBiometrics.js`

- `FaceScanProcessor.js` - uses the FaceTec web SDK and makes API calls to the
  provided APIs. This is where the FaceTec web sdk flows are being controlled
  and where scan results are handled.

- `API.js` - api requests with `axios`, you can pass in your APIs via the `api`
  argument.
- `FaceTecStyleConfig.js` - styling settings for the FaceTec SDK

All functions are agnostic, just need to provide the needed arguments so you can
use them with your system setup.

To use biometrics simply import `FaceBiometrics.js` and your webapp now has
biometric capabilities.

## Things to keep in mind

- **_FaceTec web SDK is not supported on all browsers, check [compatibility
  matrix](https://dev.facetec.com/compatibility) for more info!_**

- The FaceTec web SDK needs to be updated every **90 days** as per FaceTec
  policy, read [update policy](https://dev.facetec.com/updating#update-policy).
  Check [developer update
  log](https://github.com/TontineTrust/facetec-web-sdk/issues/1). After each
  updated the log needs to be updated manually and the next SDK update needs to
  be scheduled.

- Updating the FaceTec web SDK is done manually, meaning you have to go to the
  SDK download page, download the latest version and overwrite the previous
  version we have in this repo with the new version.

## How to update the FaceTec web SDK

- Go to [facetec sdk download page](https://dev.facetec.com/downloads)

- Click the browser button !

- Click "No, just download the SDK without configuration"

- Extract the contents of the downloaded zip file `FaceTecSDK-browser-{whatever
version}.zip`

- Open `core-sdk` and copy the contents to `facetec` root dir (make sure to
  overwrite everything)

Now the SDK should be updated.

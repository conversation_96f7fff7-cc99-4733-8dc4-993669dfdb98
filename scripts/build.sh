#!/usr/bin/env bash

# Generates a distribution bundle using vite bundler

# You can pass three of the available arguments to the script to generate a bundle with
# certain set of APIs, for example passing in staging, generates a bundle with staging APIs,
# the app is always built in production mode, the arguments only change config
# files located in /src/config

# If no arguments are passed in the app is built with production APIs by default

# Usage:
#   ./build.sh production
#   ./build.sh staging
#   ./build.sh development

# Building lite variations:
# ./build.sh production lite
# ./build.sh staging lite
# ./build.sh development lite

# Adding lite as the 2nd argument, builds the webapp in lite mode

readonly MY_TT_DIR="$(dirname "$0")"
cd $MY_TT_DIR

# Vite takes in variables like this
# Adding the VITE_ prefix to the variable name will expose it in code
ENVIRONMENT=${1:-production} VITE_BUILD_VARIATION=$2 INSTRUMENT_CODE=${3:-false} npm run build

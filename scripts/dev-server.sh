#!/usr/bin/env bash

info() {
  echo '<<< Starting PREVIEW server and serving build >>>'
}

usage() {
  echo "
  Usage: [-p port] [-h host] [-m mode] [-e environment] [-i instrument-code]

  Options:
  -m mode: 'lite', default is 'full' version
  -e environment: 'production', 'staging', 'development'
  -i instrument-code: 'true' or 'false', default is false
  -p port: 8080, default is 9000
  -h host: 0.0.0.0 default is localhost
  "
}

parse_arguments() {
  while getopts ":p:h:i:m:e:" opt; do
    case $opt in
    e)
      ENVIRONMENT="$OPTARG"
      # Check if the environment is allowed
      if [[ ! " ${allowed_environments[@]} " =~ " ${ENVIRONMENT} " ]]; then
        echo -e "Invalid environment: $ENVIRONMENT" >&2
        echo "Allowed values: ${allowed_environments[@]}"
        usage
        exit 1
      fi
      ;;
    p)
      PORT="$OPTARG"
      ;;
    h)
      HOST="$OPTARG"
      ;;
    m)
      VITE_BUILD_VARIATION="$OPTARG"
      if [[ ! " ${allowed_variations[@]} " =~ " ${VITE_BUILD_VARIATION} " ]]; then
        echo -e "Invalid mode: $VITE_BUILD_VARIATION" >&2
        echo "Allowed values: ${allowed_variations[@]}"
        usage
        exit 1
      fi
      ;;
    i)
      INSTRUMENT_CODE="$OPTARG"
      ;;
    \?)
      echo "Invalid option: -$OPTARG" >&2
      usage
      exit 1
      ;;
    :)
      echo "Option -$OPTARG requires an argument." >&2
      exit 1
      ;;
    esac
  done
}

# Define allowed values for options
allowed_environments=("development" "staging" "production")
allowed_variations=("lite" "full")

# Values to set passed by the user
PORT=9000
HOST=
INSTRUMENT_CODE=false
VITE_BUILD_VARIATION=full
ENVIRONMENT=

# Parse options and do argument values check
parse_arguments "$@"

readonly MY_TT_DIR="$(dirname "$0")"
cd $MY_TT_DIR

echo -e ">> Dev build mode: $VITE_BUILD_VARIATION << \n"

if [[ $ENVIRONMENT == 'production' ]]; then
  # Generates a preview production bundle with production APIs
  echo -e 'Generating prod build with production APIs     '
  echo -e 'Using config: /src/config/production.js\n'
  ./build.sh production $VITE_BUILD_VARIATION
  # Serves the generated production bundle locally
  info
  PORT=$PORT npm run serve
elif [[ $ENVIRONMENT == 'staging' ]]; then
  # Generates a production bundle with staging APIs
  echo -e 'Generating production build with staging APIs  '
  echo -e 'Using config: /src/config/staging.js  \n '
  ./build.sh staging $VITE_BUILD_VARIATION
  # Serves the generated production bundle locally
  info
  PORT=$PORT npm run serve
elif [[ $ENVIRONMENT == 'development' ]]; then
  # This mode is mostly used for running tests e2e
  echo -e 'Generating production build with local dev APIs    '
  echo -e 'Using config: /src/config/development.js  \n '
  # Generates a production bundle with development APIs
  # Code instrumentation can only be set to true here
  ./build.sh development $VITE_BUILD_VARIATION $INSTRUMENT_CODE
  # Serves the generated production bundle locally
  info
  PORT=$PORT HOST=$HOST npm run serve
else
  # Vite does not need to generate a bundle,
  # everything is handled by the vite dev server
  echo 'INFO: -e (build environment) value not specified using vite dev server'
  echo '<<< Starting local dev server with config: /src/config/development.js >>>'
  PORT=8080 ENVIRONMENT=development VITE_BUILD_VARIATION=$VITE_BUILD_VARIATION npm run dev
fi
echo

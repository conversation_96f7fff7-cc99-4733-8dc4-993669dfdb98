# MyTontine webapp

## Stack

![React](https://img.shields.io/badge/react-%2320232a.svg?style=for-the-badge&logo=react&logoColor=%2361DAFB)
![JavaScript](https://img.shields.io/badge/javascript-%23323330.svg?style=for-the-badge&logo=javascript&logoColor=%23F7DF1E)
![TypeScript](https://img.shields.io/badge/typescript-%23007ACC.svg?style=for-the-badge&logo=typescript&logoColor=white)
![SASS](https://img.shields.io/badge/SASS-hotpink.svg?style=for-the-badge&logo=SASS&logoColor=white)
![Vite](https://img.shields.io/badge/vite-%23646CFF.svg?style=for-the-badge&logo=vite&logoColor=white)
![cypress](https://img.shields.io/badge/-cypress-%23E5E5E5?style=for-the-badge&logo=cypress&logoColor=058a5e)
![Biome](https://img.shields.io/badge/-Biome-60A5FA?style=flat&logo=biome&logoColor=white)

## Services

[![Netlify
Status](https://api.netlify.com/api/v1/badges/4cb69ee6-1e7c-4a3c-9585-51b7b1d2a0ed/deploy-status)](https://app.netlify.com/sites/my-tontine/deploys)
![locize](https://img.shields.io/badge/dynamic/json.svg?style=plastic&color=2096F3&label=locize&query=%24.translatedPercentage&url=https://api.locize.app/badgedata/20bdd677-f708-4430-93a9-6cd17a941864&suffix=%+translated&link=https://www.locize.com)
[![Cypress E2E
tests](https://github.com/TontineTrust/mytontine-webapp/actions/workflows/cypress.yaml/badge.svg)](https://github.com/TontineTrust/mytontine-webapp/actions/workflows/cypress.yaml)

**Table of contents**

- [MyTontine webapp](#mytontine-webapp)
  - [Stack](#stack)
  - [Services](#services)
  - [Branches](#branches)
  - [Development setup](#development-setup)
    - [Quick start](#quick-start)
    - [Development workflow](#development-workflow)
    - [Generating different env bundles and previewing on local machine](#generating-different-env-bundles-and-previewing-on-local-machine)
    - [Developer setup using `robo-actuary-docker` (MyTontine backend)](#developer-setup-using-robo-actuary-docker-mytontine-backend)
    - [E2E on local machine with `robo-actuary-docker`](#e2e-on-local-machine-with-robo-actuary-docker)
  - [XState v5 state manager](#xstate-v5-state-manager)
    - [Typical machine directory structure for global machine](#typical-machine-directory-structure-for-global-machine)
    - [AuthMachine (global)](#authmachine-global)
    - [LegalMachine (global)](#legalmachine-global)
    - [BankMachine (might be deprecated and removed)](#bankmachine-might-be-deprecated-and-removed)
  - [Testing](#testing)
    - [Cypress](#cypress)
    - [Cypress directory structure](#cypress-directory-structure)
    - [Scripts to run tests](#scripts-to-run-tests)
    - [Test coverage](#test-coverage)
  - [Styling](#styling)
  - [CMS and localization](#cms-and-localization)
- [FaceTec (biometric authentication)](#facetec-biometric-authentication)
- [MyTontine Lite](#mytontine-lite)
  - [Branches](#branches-1)
  - [Development setup](#development-setup-1)

## Branches

[`staging` - deployed default branch that deploys the app with staging APIs
**(unstable)**](https://5t4g1ng.robotontine.com/)

[![Netlify
Status](https://api.netlify.com/api/v1/badges/886426cb-3749-41ea-9131-a0ec9b3b66fe/deploy-status)](https://app.netlify.com/sites/5t4g1ng/deploys)

[`prod-my-tontine` - deployed production branch
**(stable)**](https://app.mytontine.com/)

[![Netlify
Status](https://api.netlify.com/api/v1/badges/4cb69ee6-1e7c-4a3c-9585-51b7b1d2a0ed/deploy-status)](https://app.netlify.com/sites/my-tontine/deploys)

Deploy config for whole repo can be found in root under `netlify.toml`

## Development setup

_Note: The app only supports node version >
[16](https://nodejs.org/en/blog/announcements/nodejs16-eol)_

### Quick start

1. Make sure you have [node](https://nodejs.org/en) on your machine first
2. Use `npm ci` to install project dependencies
3. **Make sure you have access to
   [facetec-web-sdk](https://github.com/TontineTrust/facetec-web-sdk) and have
   configured your github account to use SSH keys**
4. Make sure you have locize keys `.env.local`
5. Use `npm run dev:mt` to run the project where the `facetec-web-sdk` repo will
   be pulled in as a git submodule. This will run the dev server with type checks, linting and code
   formatting checks.

   5.1 Alternatively you can use `./scripts/dev-server.sh` to skip all
   checks, but this will **NOT** pull the `facetec-web-sdk` repo, run only if
   you already have pulled the repo.

### Development workflow

To do **typecheck** simply run `npm run type-check`

To do **code linting**, **type linting**, **style linting** with BEM check and a
**format check** all at once simply use `npm run lint:format`

### Generating different env bundles and previewing on local machine

Apart from dev server all different build environments can be previewed locally,
by passing the following values for `-e` `development` `staging` `production`.
This builds the app in production mode using `development.js` config file which
has the backend URL's pointed to localhost.

**Script usage:**

Usage: [-p port] [-h host] [-m mode] [-e environment] [-i instrument-code]

Options:

- -m mode: 'lite', default is 'full' version
- -e environment: 'production', 'staging', 'development'
- -i instrument-code: 'true' or 'false', default is false
- -p port: 8080, default is 9000
- -h host: 0.0.0.0 default is localhost

Example to generate production ready bundle with `localhost` backend APIs

```shell
npm run dev:mt -- -e development
```

To analyze the bundle size in production use

```shell
npm run build-analyze
```

### Developer setup using `robo-actuary-docker` (MyTontine backend)

Visit
[`robo-actuary-docker`](https://github.com/TontineTrust/robo-actuary-docker-compose)
and follow the instructions to clone the dockerized backend and run it locally

### E2E on local machine with `robo-actuary-docker`

**_Make sure you have completed [Developer setup using `robo-actuary-docker` (MyTontine
backend)](#developer-setup-using-robo-actuary-mytontine-backend), otherwise this
won't work at all_**

Important thing to note is that the tests are ran against the `staging` branch
on `robo-actuary-docker`

How to start E2E tests with `robo-actuary-docker` on your local machine:

1. Make sure to stop any server running on `robo-actuary-docker`
2. Make sure to stop any vite server dev or preview
3. Run `/cypress/cypress.sh`

## XState v5 state manager

Following the feature based project architecture, each feature should have it's
own state connected to the global app state. For now multiple features share the
same state machine, this will change in the future

Useful XState 5 docs

- [XState 5 docs](https://stately.ai/docs)
- [XState type docs](https://stately.ai/docs/typescript)

### Typical machine directory structure for global machine

Auth machine structure used as an example, but any global machine that needs to
interact with global data needs to follow the following structure:

- `AuthMachineProvider.tsx` - imported on the highest level in the main
  component tree, provides interpreted values from the `AuthMachine`. Uses
  [actor
  context](https://stately.ai/blog/2023-1-27-making-state-machines-global-in-react#creating-an-actor-context)
  to interpret the machine so it can be used globally.
- `useAccountService.ts` - custom hook interface which wraps
  [`useActorRef`](https://stately.ai/docs/xstate-react#useactorrefmachine-options)
  and adds some additional functionalities for better dev experience.
- `AuthMachineServices.ts` - services written in TypeScript, which are API
  requests to the `UserAccountService` on the backend. In xstate 5 these are no
  longer called services they are called actors and we use them as promise
  actors.
- `AuthMachineActions.ts` - action objects where can be used with any type of
  [**built-in**](https://stately.ai/docs/actions#built-in-actions) machine
  actions. For typing reasons do not directly wrap the action object with a
  built-in action and export it. [More on XState 5
  actions](https://stately.ai/docs/actions)

  ```javascript
  // In your machine config file
  import { clearAuthContext } from './AuthMachineActions'

  // In the setup block of your machine
  actions: {
    // This will automatically type everything and save you a lot of headache
    clearAuthContext: assign(clearAuthContext)
  }
  ```

### AuthMachine (global)

Located in `src/features/authentication` provides:

- Authenticating to the MyTontine app and session control
- User data management
- Biometric authentication
- Payout account information management
- Interaction with our referral program
- `Biometrics.ts` adds biometrics to the `AuthMachine` services. **Recommended
  usage trough `AuthMachine`** only.

### LegalMachine (global)

Located in `src/features/agreements`, uses backend APIs that are related to
legal operations and handles state for forms and agreements.

- Fetching, signing and re-signing agreements
- Generating data collected from the UI into a PDF file that is ready for the
  user to sign
- Saving the progress and retrieving the saved progress on large multi step form

### BankMachine (might be deprecated and removed)

Located in `src/features/banking`. Does the following:

- Fetching banking information
- Fetching referral rewards data

## Testing

### Cypress

As a main testing tool we use [Cypress](https://www.cypress.io/) that offers

- Component testing (`cypress/component`)
- End to end testing (`cypress/e2e`)
- Visual testing with [Percy](https://percy.io/e2ee60a4/MyTontine) (**Cypress
  addon**)

### Cypress directory structure

Config file is located in root `/cypress/cypress.config.ts`

Tests are located in the root of the project under the `cypress` directory,
where there are two types of tests `component` and `e2e` tests with their
respective directories.

- `component` contains component tests and matches the `src` directory
  structure. By component tests what is actually meant is unit tests where a
  single function is tested and integration tests where a React component is
  tested in isolation
- `e2e` are end to end tests, where the application features are tested. Each
  file in the `e2e` eludes to an app's feature.

Most basic tests that should be always included when writing a feature test:

1. **Basic UI tests**, if UI elements work and have the desired behavior
2. **Authorization**, whether the user needs `read` or `write` permissions to
   access a certain feature
3. **KYC**, whether the user needs to have fully completed KYC in order to
   access that feature partially or fully

### Scripts to run tests

Available scripts from `package.json`

```shell
/cypress/cypress.sh
```

Only works if [Developer setup using `robo-actuary` (MyTontine
backend)](#developer-setup-using-robo-actuary-mytontine-backend) is completed.

Starts necessary `robo-actuary` services that Cypress E2E tests need to run the
app against.

To open the cypress GUI where you can choose what tests to run in which browser.
Different browsers appear in the GUI only if they are installed on your local
machine

```shell
npm run cypress-open
```

To run e2e tests in headless mode

```shell
npm run cypress-headless
```

```shell
npm run cypress:ct
```

To run unit tests with cypress use

```shell
npm run cypress:ut
```

---

**WARNING: If you run `cypress-headless` locally Percy will capture snapshots,
use this only with CI/CD config**

For documentation, guides and more information about `Percy` and `Cypress` click
[here](https://docs.cypress.io/guides/overview/why-cypress)

### Test coverage

The code is instrumented using [vite istanbul
plugin](https://www.npmjs.com/package/vite-plugin-istanbul) and the code
coverage reporting is done with Cypress code coverage plugin. For more details
on the setup refer to the [code coverage with cypress
documentation](https://docs.cypress.io/guides/tooling/code-coverage).

Code coverage from unit, component and e2e tests after run is merged into one
report to show full coverage.

Code coverage will only be reported if the env variable `INSTRUMENT_CODE` is set
to `true`. The code is only instrumented in `development` build preview mode.

Check `scripts/dev-server.sh` for more details on bundles and how the instrument
code env is setup.

The code instrument config can be found in `vite.config.ts`, look for
`istanbul` object

## Styling

The codebase uses `scss` modules where each component styling is scoped.

CSS linting is done by the `stylelinter` uses `stylelint-scss` and
`stylelint-selector-bem-pattern` as plugins for SCSS and BEM convention, when
CSS is linted it will be checked if it has SCSS errors and if it is following
the BEM convention

The linter uses `stylelint-config-recommended-scss` rules to lint the SCSS

Stylelint config file is located in the root of the project `.stylelintrc.json`

To lint SCSS use `npm run lint-scss`

**Important** In order to mark a `.scss` file to be linted with the BEM plugin
first you need to add a comment as shown in the example

**Example:**

```css
/** @define mycomponent */
.mycomponent {
}
```

The comment (`/** @define mycomponent */`) must **match** the class selector in
order for the SCSS linter to check if the SCSS is following the BEM convention

## CMS and localization

The application uses the `i18next` react library to manage the app's content and
localization

For full `i18next` documentation go [here](https://www.i18next.com/)

# FaceTec (biometric authentication)

Main authentication method for users to get securely authenticated to the
MyTontine webapp.

The frontend web SDK is provided by FaceTec. We use the FaceTec [web
sdk](https://dev.facetec.com/downloads) in order to provide biometric
authentication for our app.

Our version of the SDK and our APIs lives
[here](https://github.com/TontineTrust/facetec-web-sdk) and is implemented as a
git submodule for now. In order to successfully to build this app, the
facetec-web-sdk submodule is needed. The submodule can only be cloned via SSH,
meaning that you will have to setup SSH on github on your own
[here](https://docs.github.com/en/authentication/connecting-to-github-with-ssh)

Ignore the vite warning for FaceTecSDK.js . At the moment FaceTec does not
support vite out of the box, so the SDK is only loaded as a script in
`index.html`.

# MyTontine Lite

Stripped down version of the webapp that is embedded via an iframe on the
tontine.com website in order for users to use the tontinator and the referral
programme. Allows the users to pre-register on our app.

To control what renders and what does not render on this version of the app we
use `isLite` variable which is located in `src/config/lite.js`

## Branches

[`prod-my-tontine-lite`](https://mytontinelite.netlify.app/)

[![Netlify
Status](https://api.netlify.com/api/v1/badges/7055df94-f80b-42b0-aab0-7a187645c42c/deploy-status)](https://app.netlify.com/sites/mytontinelite/deploys)

[`staging-my-tontine-lite`](https://staging-mytontinelite.netlify.app/)

[![Netlify
Status](https://api.netlify.com/api/v1/badges/b4ab3619-9706-4240-824a-6ef2853674ab/deploy-status)](https://app.netlify.com/sites/staging-mytontinelite/deploys)

## Development setup

The development setup is exactly the same. To run a lite build in dev mode
simply use

```shell
npm run dev:mt -- -m lite
```

[Click here for more info on how to generate Lite builds
here](#generating-different-env-bundles-and-previewing-on-local-machine)
